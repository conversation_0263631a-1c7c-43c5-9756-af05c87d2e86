﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Events.Queries;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    public class GetUserHandler : IRequestHandler<GetUserQuery, Result<GetUserResponseDto>>
    {
        private readonly IUserService _userService;

        public GetUserHandler(IUserService userService)
        {
            _userService = userService;
        }

        public async Task<Result<GetUserResponseDto>> Handle(
            GetUserQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var userResult = await _userService.GetUserById(request.UserId);
                if (userResult.IsFailed)
                {
                    return userResult.ToResult<GetUserResponseDto>();
                }

                var user = userResult.Value;
                var tokenResult = await _userService.GetJwtTokenAsync(user);
                if (tokenResult.IsFailed)
                {
                    return tokenResult.ToResult<GetUserResponseDto>();
                }

                var token = tokenResult.Value;
                var userDto = new GetUserResponseDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    EmailConfirmed = user.EmailConfirmed,
                    Token = token,
                };

                return Result.Ok(userDto);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
