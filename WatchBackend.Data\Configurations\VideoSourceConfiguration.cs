﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;
using WatchBackend.Core.Entities;

namespace WatchBackend.Data.Configurations
{
    public class VideoSourceConfiguration : IEntityTypeConfiguration<VideoSourceEntity>
    {
        public void Configure(EntityTypeBuilder<VideoSourceEntity> builder)
        {
            var options = new JsonSerializerOptions(JsonSerializerDefaults.General);

            builder
                .Property(c => c.Attributes)
                .HasConversion(
                    a => JsonSerializer.Serialize(a, options),
                    s => JsonSerializer.Deserialize<IDictionary<string, string>>(s, options),
                    ValueComparer.CreateDefault(typeof(IDictionary<string, string>), true)
                );
        }
    }
}
