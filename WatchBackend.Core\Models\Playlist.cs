﻿using System.Collections;
using System.Diagnostics.CodeAnalysis;

namespace WatchBackend.Core.Models
{
    public class Playlist<TItem> : IReadOnlyList<TItem>
    {
        private readonly List<TItem> _items;
        private int _currentIndex;

        public required int Id { get; set; }

        public required int CurrentIndex
        {
            get { return _currentIndex; }
            init { _currentIndex = value; }
        }

        public TItem? CurrentItem
        {
            get
            {
                if (_items.Count > _currentIndex)
                {
                    return _items[_currentIndex];
                }

                return default;
            }
        }

        public required IReadOnlyList<TItem> Items
        {
            get { return _items; }
            [MemberNotNull(nameof(_items))]
            init { _items = value.ToList(); }
        }

        public int Count => _items.Count;

        public TItem this[int index] => _items[index];

        public void Add(TItem item)
        {
            _items.Add(item);
        }

        public void AddRange(IEnumerable<TItem> items)
        {
            _items.AddRange(items);
        }

        public void Move(int oldIndex, int newIndex)
        {
            EnsureIndexBoundsAgainstCurrentItems(oldIndex);
            EnsureIndexBoundsAgainstRemovedItem(newIndex);

            var removedItem = _items[oldIndex];

            _items.RemoveAt(oldIndex);
            _items.Insert(newIndex, removedItem);
        }

        public void SetCurrentIndex(int newIndex)
        {
            EnsureIndexBoundsAgainstCurrentItems(newIndex);

            _currentIndex = newIndex;
        }

        public TItem? AdvanceToNextItem()
        {
            if (_currentIndex + 1 < _items.Count)
            {
                return default;
            }

            _currentIndex++;

            return _items[_currentIndex];
        }

        public void EnsureIndexBoundsAgainstCurrentItems(int index)
        {
            if (index < 0)
            {
                throw new ArgumentOutOfRangeException(nameof(index), "Index can't be lower than 0");
            }

            if (index == 0)
            {
                return;
            }

            if (index >= _items.Count)
            {
                throw new ArgumentOutOfRangeException(
                    nameof(index),
                    "Index can't be higher or equal item count"
                );
            }
        }

        public void EnsureIndexBoundsAgainstRemovedItem(int index)
        {
            if (index < 0)
            {
                throw new ArgumentOutOfRangeException(nameof(index), "Index can't be lower than 0");
            }

            if (index == 0)
            {
                return;
            }

            if (index >= _items.Count - 1)
            {
                throw new ArgumentOutOfRangeException(
                    nameof(index),
                    "Index can't be higher or equal item count"
                );
            }
        }

        public static Playlist<TItem> Create()
        {
            return new Playlist<TItem>
            {
                Id = default,
                Items = [],
                CurrentIndex = default,
            };
        }

        public IEnumerator<TItem> GetEnumerator()
        {
            return _items.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}
