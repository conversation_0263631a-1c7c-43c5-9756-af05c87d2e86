﻿using System.Security.Claims;
using FluentResults;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Extensions;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Models;
using WatchBackend.Web.Extensions;
using WatchBackend.Web.Mapping;

namespace WatchBackend.Web.Hubs
{
    [Authorize]
    public class RoomHub : Hub
    {
        private static readonly ConnectionToRoomsMapping _connectionToRooms = new();

        private readonly IRoomService _roomService;
        private readonly IUserService _userService;
        private readonly ISourceService _sourceService;

        public RoomHub(
            IRoomService roomService,
            IUserService userService,
            ISourceService sourceService
        )
        {
            _roomService = roomService;
            _userService = userService;
            _sourceService = sourceService;
        }

        public async Task<ResultDto> JoinRoom(string roomIdHash)
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result.Fail(new InvalidError("User id is not provided")).ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResultDto();
            }

            var userId = userIdResult.Value;
            var userResult = await this._userService.GetUserById(userId);
            if (userResult.IsFailed)
            {
                return userResult.ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResultDto();
            }

            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResultDto();
            }

            // Change room user name if it's been updated
            var user = userResult.Value;
            var roomUser = roomUserResult.Value;
            if (roomUser.Username != user.Username)
            {
                roomUser.Username = user.Username;
                _roomService.SaveRoomDebounced(room);
            }

            var isUserAlreadyConnected = roomUser.IsConnected;
            roomUser.AddConnectionId(Context.ConnectionId);

            _connectionToRooms.Add(Context.ConnectionId, room);

            await Groups.AddToGroupAsync(Context.ConnectionId, roomIdHash);

            if (!isUserAlreadyConnected)
            {
                await OnUserEnteredRoom(room, roomUser);
            }

            return Result.Ok().ToResultDto();
        }

        public async Task<ResultDto<RoomUserDto>> GetCurrentRoomUser(string roomIdHash)
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result
                    .Fail(new InvalidError("User id is not provided"))
                    .ToResult<RoomUserDto>()
                    .ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResult<RoomUserDto>().ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResult<RoomUserDto>().ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResult<RoomUserDto>().ToResultDto();
            }

            var roomUser = roomUserResult.Value;
            var roomUserDto = roomUser.Adapt<RoomUserDto>();

            return Result.Ok(roomUserDto).ToResultDto();
        }

        public async Task<ResultDto<IEnumerable<RoomUserDto>>> GetAllConnectedRoomUsers(
            string roomIdHash
        )
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result
                    .Fail(new InvalidError("User id is not provided"))
                    .ToResult<IEnumerable<RoomUserDto>>()
                    .ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResult<IEnumerable<RoomUserDto>>().ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResult<IEnumerable<RoomUserDto>>().ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResult<IEnumerable<RoomUserDto>>().ToResultDto();
            }

            var allConnectedRoomUsers = room.GetAllConnectedUsers();
            var allConnectedRoomUsersDtos = allConnectedRoomUsers.Adapt<IEnumerable<RoomUserDto>>();

            return Result.Ok(allConnectedRoomUsersDtos).ToResultDto();
        }

        public async Task<ResultDto> SetVideo(string roomIdHash, string videoUrl)
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result.Fail(new InvalidError("User id is not provided")).ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResultDto();
            }

            var roomUser = roomUserResult.Value;
            if (roomUser.Role < RoomUserRoles.Administrator)
            {
                return Result
                    .Fail(new UnauthorizedError("User is not authorized to set source"))
                    .ToResultDto();
            }

            var sourceContainerResult = await _sourceService.GetSourceFromUrlAsync(videoUrl);
            if (sourceContainerResult.IsFailed)
            {
                return sourceContainerResult.ToResultDto();
            }

            var sourceContainer = sourceContainerResult.Value;
            await _roomService.AddSourceToPlaylist(room, sourceContainer, setAsCurrentSource: true);

            var recipients = Clients.Group(roomIdHash);
            await OnCurrentSourceChanged(room, recipients);

            var playbackStateSource = new PausePlaybackStateSourceDto();
            await OnPlaybackStateChanged(room, recipients, playbackStateSource);

            return Result.Ok().ToResultDto();
        }

        public async Task<ResultDto<SourceContainerDto?>> GetCurrentSource(string roomIdHash)
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result
                    .Fail(new InvalidError("User id is not provided"))
                    .ToResult<SourceContainerDto?>()
                    .ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResult<SourceContainerDto?>().ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResult<SourceContainerDto?>().ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResult<SourceContainerDto?>().ToResultDto();
            }

            var sourceContainer = room.Playlist.CurrentItem;
            var sourceContainerDto = sourceContainer?.Adapt<SourceContainerDto>();

            return Result.Ok(sourceContainerDto).ToResultDto();
        }

        public async Task<ResultDto<PlaybackInfoDto>> GetPlaybackInfo(string roomIdHash)
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result
                    .Fail(new InvalidError("User id is not provided"))
                    .ToResult<PlaybackInfoDto>()
                    .ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResult<PlaybackInfoDto>().ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResult<PlaybackInfoDto>().ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResult<PlaybackInfoDto>().ToResultDto();
            }

            var playbackInfoDto = new PlaybackInfoDto
            {
                PlaybackState = room.PlaybackState,
                CurrentTimeInSeconds = room.CurrentTime.TotalSeconds,
            };

            return Result.Ok(playbackInfoDto).ToResultDto();
        }

        public async Task<ResultDto> PlaySource(string roomIdHash)
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result.Fail(new InvalidError("User id is not provided")).ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResultDto();
            }

            var roomUser = roomUserResult.Value;
            if (roomUser.Role < RoomUserRoles.Viewer)
            {
                return Result
                    .Fail(new UnauthorizedError("User is not authorized to play source"))
                    .ToResultDto();
            }

            if (room.PlaybackState == PlaybackState.Playing)
            {
                return Result.Ok().ToResultDto();
            }

            room.PlaySource();

            var playbackStateSource = new PlayPlaybackStateSourceDto();
            var playbackStateChangedRecipients = Clients.OthersInGroup(roomIdHash);
            await OnPlaybackStateChanged(room, playbackStateChangedRecipients, playbackStateSource);

            return Result.Ok().ToResultDto();
        }

        public async Task<ResultDto> PauseSource(string roomIdHash, double pauseTimeInSeconds)
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result.Fail(new InvalidError("User id is not provided")).ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResultDto();
            }

            var roomUser = roomUserResult.Value;
            if (roomUser.Role < RoomUserRoles.Viewer)
            {
                return Result
                    .Fail(new UnauthorizedError("User is not authorized to pause source"))
                    .ToResultDto();
            }

            room.PauseSource(TimeSpan.FromSeconds(pauseTimeInSeconds));

            var playbackStateSource = new PausePlaybackStateSourceDto();
            var playbackStateChangedRecipients = Clients.OthersInGroup(roomIdHash);
            await OnPlaybackStateChanged(room, playbackStateChangedRecipients, playbackStateSource);

            _roomService.SaveRoomDebounced(room);

            return Result.Ok().ToResultDto();
        }

        public async Task<ResultDto> SkipSourceTo(
            string roomIdHash,
            double skipTimeInSeconds,
            int keyboardSeekDeltaInSeconds
        )
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result.Fail(new InvalidError("User id is not provided")).ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResultDto();
            }

            var roomUser = roomUserResult.Value;
            if (roomUser.Role < RoomUserRoles.Viewer)
            {
                return Result
                    .Fail(new UnauthorizedError("User is not authorized to skip source"))
                    .ToResultDto();
            }

            room.SkipSourceTo(TimeSpan.FromSeconds(skipTimeInSeconds));

            BasePlaybackStateSourceDto playbackStateSource;
            if (keyboardSeekDeltaInSeconds != 0)
            {
                playbackStateSource = new SeekByKeyboardPlaybackStateSourceDto
                {
                    KeyboardSeekDeltaSeconds = keyboardSeekDeltaInSeconds,
                };
            }
            else
            {
                playbackStateSource = new SeekPlaybackStateSourceDto();
            }

            var playbackStateChangedRecipients = Clients.OthersInGroup(roomIdHash);
            await OnPlaybackStateChanged(room, playbackStateChangedRecipients, playbackStateSource);

            _roomService.SaveRoomDebounced(room);

            return Result.Ok().ToResultDto();
        }

        public async Task<ResultDto> SetSubtitlesOffset(
            string roomIdHash,
            string subtitlesSrc,
            double offset
        )
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result.Fail(new InvalidError("User id is not provided")).ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResultDto();
            }

            var roomUser = roomUserResult.Value;
            if (roomUser.Role < RoomUserRoles.Administrator)
            {
                return Result
                    .Fail(new UnauthorizedError("User is not authorized to set caption offset"))
                    .ToResultDto();
            }

            var setSubtitlesOffsetResult = room.SetSubtitlesOffset(subtitlesSrc, offset);
            if (setSubtitlesOffsetResult.IsFailed)
            {
                return setSubtitlesOffsetResult.ToResultDto();
            }

            var subtitleSource = setSubtitlesOffsetResult.Value;
            var subtitlesChangedRecipients = Clients.OthersInGroup(roomIdHash);
            await OnSubtitlesChanged(subtitleSource, subtitlesChangedRecipients);

            _roomService.SaveRoomDebounced(room);

            return Result.Ok().ToResultDto();
        }

        public async Task<ResultDto> SetUserRole(
            string roomIdHash,
            int targetUserId,
            RoomUserRoles role
        )
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                return Result.Fail(new InvalidError("User id is not provided")).ToResultDto();
            }

            if (userIdResult.IsFailed)
            {
                return userIdResult.ToResultDto();
            }

            var roomResult = await _roomService.GetRoomByIdHashAsync(roomIdHash);
            if (roomResult.IsFailed)
            {
                return roomResult.ToResultDto();
            }

            var userId = userIdResult.Value;
            var room = roomResult.Value;

            var roomUserResult = room.GetUserByUserId(userId);
            if (roomUserResult.IsFailed)
            {
                return roomUserResult.ToResultDto();
            }

            var roomUser = roomUserResult.Value;
            if (roomUser.Role < RoomUserRoles.Moderator)
            {
                return Result
                    .Fail(new UnauthorizedError("User is not authorized to set other user's role"))
                    .ToResultDto();
            }

            var targetRoomUserResult = room.GetUserByUserId(targetUserId);
            if (targetRoomUserResult.IsFailed)
            {
                return targetRoomUserResult.ToResultDto();
            }

            var targetRoomUser = targetRoomUserResult.Value;
            if (targetRoomUser.Role >= roomUser.Role)
            {
                return Result
                    .Fail(
                        new UnauthorizedError(
                            "User is not authorized to set role of someone with equal or higher role"
                        )
                    )
                    .ToResultDto();
            }

            room.SetUserRole(targetUserId, role);

            var roomUserChangedRecipients = Clients.OthersInGroup(roomIdHash);
            await OnRoomUserChanged(targetRoomUser, roomUserChangedRecipients);

            _roomService.SaveRoomDebounced(room);

            return Result.Ok().ToResultDto();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userIdResult = this.Context.User?.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult == null)
            {
                await base.OnDisconnectedAsync(exception);
                return;
            }

            if (userIdResult.IsFailed)
            {
                await base.OnDisconnectedAsync(exception);
                return;
            }

            var userId = userIdResult.Value;
            var rooms = _connectionToRooms.GetRooms(Context.ConnectionId);
            foreach (var room in rooms)
            {
                var roomUserResult = room.GetUserByUserId(userId);
                if (roomUserResult.IsFailed)
                {
                    continue;
                }

                var roomUser = roomUserResult.Value;
                roomUser.RemoveConnectionId(Context.ConnectionId);
                if (roomUser.IsConnected)
                {
                    continue;
                }

                var roomUserDto = roomUser.Adapt<RoomUserDto>();
                var others = Clients.OthersInGroup(room.IdHash);

                await others.SendAsync("userLeftRoom", roomUserDto);

                if (room.GetAllConnectedUsers().Count() == 0)
                {
                    room.PauseSource(room.CurrentTime);
                    await _roomService.SaveRoomAsync(room);
                }
            }

            await base.OnDisconnectedAsync(exception);
        }

        private async Task OnUserEnteredRoom(Room room, RoomUser user)
        {
            var roomUserDto = user.Adapt<RoomUserDto>();
            var others = Clients.OthersInGroup(room.IdHash);

            await others.SendAsync("userEnteredRoom", roomUserDto);
        }

        private async Task OnUserLeftRoom(Room room, RoomUser user)
        {
            var roomUserDto = user.Adapt<RoomUserDto>();
            var others = Clients.OthersInGroup(room.IdHash);

            await others.SendAsync("userLeftRoom", roomUserDto);
        }

        private async Task OnCurrentSourceChanged(Room room, IClientProxy recipients)
        {
            var sourceContainer = room.Playlist.CurrentItem;
            if (sourceContainer == null)
            {
                return;
            }

            var sourceContainerDto = sourceContainer.Adapt<SourceContainerDto>();

            await recipients.SendAsync("currentSourceChanged", sourceContainerDto);
        }

        private async Task OnPlaybackStateChanged(
            Room room,
            IClientProxy recipients,
            BasePlaybackStateSourceDto playbackStateSource
        )
        {
            var playbackInfoDto = new PlaybackInfoDto
            {
                PlaybackState = room.PlaybackState,
                CurrentTimeInSeconds = room.CurrentTime.TotalSeconds,
                PlaybackStateSource = playbackStateSource,
            };

            await recipients.SendAsync("playbackStateChanged", playbackInfoDto);
        }

        private async Task OnSubtitlesChanged(
            SubtitleSource subtitleSource,
            IClientProxy recipients
        )
        {
            var subtitleSourceDto = subtitleSource.Adapt<SubtitleSourceDto>();

            await recipients.SendAsync("subtitlesChanged", subtitleSourceDto);
        }

        private async Task OnRoomUserChanged(RoomUser roomUser, IClientProxy recipients)
        {
            var roomUserDto = roomUser.Adapt<RoomUserDto>();

            await recipients.SendAsync("roomUserChanged", roomUserDto);
        }
    }
}
