﻿using Mapster;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Models;

namespace WatchBackend.Web.Mapping
{
    public class BaseExternalInfoToBaseExternalInfoDto : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config
                .ForType<BaseExternalInfo, BaseExternalInfoDto>()
                .Include<MovieInfo, MovieInfoDto>()
                .Include<SeriesInfo, SeriesInfoDto>();
        }
    }
}
