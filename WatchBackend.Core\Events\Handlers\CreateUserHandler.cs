﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    internal class CreateUserHandler
        : IRequestHandler<CreateUserCommand, Result<CreateUserResponseDto>>
    {
        private readonly IUserService _userService;

        public CreateUserHandler(IUserService userAuthenticationService)
        {
            _userService = userAuthenticationService;
        }

        public async Task<Result<CreateUserResponseDto>> Handle(
            CreateUserCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var createUserResult = await _userService.CreateUser();
                if (createUserResult.IsFailed)
                {
                    return createUserResult.ToResult<CreateUserResponseDto>();
                }

                var user = createUserResult.Value;
                var tokenResult = await _userService.GetJwtTokenAsync(user);
                if (tokenResult.IsFailed)
                {
                    return tokenResult.ToResult<CreateUserResponseDto>();
                }

                var token = tokenResult.Value;
                var createUserResultDto = new CreateUserResponseDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    EmailConfirmed = user.EmailConfirmed,
                    Token = token,
                };

                return Result.Ok(createUserResultDto);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
