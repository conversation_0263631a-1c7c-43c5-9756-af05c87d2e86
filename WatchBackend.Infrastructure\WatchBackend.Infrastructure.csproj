﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>12.0</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Templates\Email\ConfirmationEmail.html" />
    <None Remove="Templates\Email\RecoveryEmail.html" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Templates\Email\ConfirmationEmail.html" />
    <EmbeddedResource Include="Templates\Email\RecoveryEmail.html" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Hashids.net" Version="1.7.0" />
    <PackageReference Include="jcamp.FluentEmail.Bootstrap" Version="3.8.0" />
    <PackageReference Include="jcamp.FluentEmail.Mailtrap" Version="3.8.0" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.20" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="7.0.20" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="7.0.4" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="7.0.20" />
    <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="7.0.20" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="7.0.0" />
    <PackageReference Include="Syllabore" Version="2.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WatchBackend.Core\WatchBackend.Core.csproj" />
    <ProjectReference Include="..\WatchBackend.Data\WatchBackend.Data.csproj" />
  </ItemGroup>

</Project>
