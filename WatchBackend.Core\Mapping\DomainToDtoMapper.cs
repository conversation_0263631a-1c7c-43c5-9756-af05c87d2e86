using WatchBackend.Core.Dtos;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Mapping
{
    public static class DomainToDtoMapper
    {
        public static GetUserResponseDto ToGetUserResponseDto(this User user)
        {
            return new GetUserResponseDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                EmailConfirmed = user.EmailConfirmed,
                Token = string.Empty, // User doesn't carry token
            };
        }

        public static RoomUserDto ToRoomUserDto(this RoomUser roomUser)
        {
            return new RoomUserDto
            {
                Id = roomUser.Id,
                Username = roomUser.Username,
                Role = roomUser.Role,
            };
        }

        public static SourceContainerDto ToSourceContainerDto(this SourceContainer sourceContainer)
        {
            return new SourceContainerDto
            {
                Id = sourceContainer.Id,
                Type = sourceContainer.Type,
                Video = sourceContainer.Video?.ToVideoSourceDto(),
                Subtitles = sourceContainer.Subtitles?.Select(s => s.ToSubtitleSourceDto()),
                Attributes = sourceContainer.Attributes,
                ExternalInfo = sourceContainer.ExternalInfo?.ToBaseExternalInfoDto(),
            };
        }

        public static VideoSourceDto ToVideoSourceDto(this VideoSource videoSource)
        {
            return new VideoSourceDto
            {
                Url = videoSource.Url,
                Attributes = videoSource.Attributes,
                Width = videoSource.Width,
                Height = videoSource.Height,
            };
        }

        public static SubtitleSourceDto ToSubtitleSourceDto(this SubtitleSource subtitleSource)
        {
            return new SubtitleSourceDto
            {
                Url = subtitleSource.Url,
                Label = subtitleSource.Label,
                SrcLang = subtitleSource.SrcLang,
                Offset = subtitleSource.Offset,
                Attributes = subtitleSource.Attributes,
            };
        }

        public static BaseExternalInfoDto ToBaseExternalInfoDto(this BaseExternalInfo externalInfo)
        {
            return externalInfo switch
            {
                SeriesInfo seriesInfo => seriesInfo.ToSeriesInfoDto(),
                MovieInfo movieInfo => movieInfo.ToMovieInfoDto(),
                _ => new BaseExternalInfoDto { Title = externalInfo.Title },
            };
        }

        public static SeriesInfoDto ToSeriesInfoDto(this SeriesInfo seriesInfo)
        {
            return new SeriesInfoDto
            {
                Title = seriesInfo.Title,
                OriginalTitle = seriesInfo.OriginalTitle,
                Description = seriesInfo.Description,
                ReleaseDate = seriesInfo.ReleaseDate,
                PosterImageUrl = seriesInfo.PosterImageUrl,
                BackdropImageUrl = seriesInfo.BackdropImageUrl,
                BackdropPlaceholderUrl = seriesInfo.BackdropPlaceholderUrl,
                ImdbId = seriesInfo.ImdbId,
                OriginCountries = seriesInfo.OriginCountries,
                Genres = seriesInfo.Genres,
                SpokenLanguages = seriesInfo.SpokenLanguages,
                Networks = seriesInfo.Networks?.Select(c => c.ToCompanyDto()) ?? [],
                ProductionCompanies =
                    seriesInfo.ProductionCompanies?.Select(c => c.ToCompanyDto()) ?? [],
                SeasonNumber = seriesInfo.SeasonNumber,
                EpisodeNumber = seriesInfo.EpisodeNumber,
                EpisodeTitle = seriesInfo.EpisodeTitle,
                Credits = seriesInfo.Credits?.ToSeriesCreditsDto() ?? SeriesCreditsDto.Create(),
            };
        }

        public static MovieInfoDto ToMovieInfoDto(this MovieInfo movieInfo)
        {
            return new MovieInfoDto
            {
                Title = movieInfo.Title,
                OriginalTitle = movieInfo.OriginalTitle,
                Description = movieInfo.Description,
                ReleaseDate = movieInfo.ReleaseDate,
                PosterImageUrl = movieInfo.PosterImageUrl,
                BackdropImageUrl = movieInfo.BackdropImageUrl,
                BackdropPlaceholderUrl = movieInfo.BackdropPlaceholderUrl,
                ImdbId = movieInfo.ImdbId,
                OriginCountries = movieInfo.OriginCountries,
                Genres = movieInfo.Genres,
                SpokenLanguages = movieInfo.SpokenLanguages,
                Networks = movieInfo.Networks?.Select(c => c.ToCompanyDto()) ?? [],
                ProductionCompanies =
                    movieInfo.ProductionCompanies?.Select(c => c.ToCompanyDto()) ?? [],
                Tagline = movieInfo.Tagline,
                Budget = movieInfo.Budget,
                Credits = movieInfo.Credits?.ToMovieCreditsDto() ?? MovieCreditsDto.Create(),
            };
        }

        public static SeriesCreditsDto ToSeriesCreditsDto(this SeriesCredits seriesCredits)
        {
            return new SeriesCreditsDto
            {
                Actors = seriesCredits.Actors?.Select(a => a.ToActorDto()) ?? [],
                Writers = seriesCredits.Writers?.Select(w => w.ToCrewDto()) ?? [],
                Directors = seriesCredits.Directors?.Select(d => d.ToCrewDto()) ?? [],
                OriginalMusicComposers =
                    seriesCredits.OriginalMusicComposers?.Select(c => c.ToCrewDto()) ?? [],
                GuestActors = seriesCredits.GuestActors?.Select(a => a.ToActorDto()) ?? [],
                Creators = seriesCredits.Creators?.Select(c => c.ToCrewDto()) ?? [],
            };
        }

        public static MovieCreditsDto ToMovieCreditsDto(this MovieCredits movieCredits)
        {
            return new MovieCreditsDto
            {
                Actors = movieCredits.Actors?.Select(a => a.ToActorDto()) ?? [],
                Writers = movieCredits.Writers?.Select(w => w.ToCrewDto()) ?? [],
                Directors = movieCredits.Directors?.Select(d => d.ToCrewDto()) ?? [],
                OriginalMusicComposers =
                    movieCredits.OriginalMusicComposers?.Select(c => c.ToCrewDto()) ?? [],
            };
        }

        public static ActorDto ToActorDto(this Actor actor)
        {
            return new ActorDto
            {
                Name = actor.Name,
                ProfileImageUrl = actor.ProfileImageUrl,
                Character = actor.Character,
                Order = actor.Order,
            };
        }

        public static CrewDto ToCrewDto(this Crew crew)
        {
            return new CrewDto { Name = crew.Name, ProfileImageUrl = crew.ProfileImageUrl };
        }

        public static CompanyDto ToCompanyDto(this Company company)
        {
            return new CompanyDto { Name = company.Name, LogoImageUrl = company.LogoImageUrl };
        }
    }
}
