﻿using WatchBackend.Core.Dtos;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Mapping
{
    public static class DomainToDtoMapper
    {
        public static GetUserResponseDto ToGetUserResponseDto(this User user)
        {
            return new GetUserResponseDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                EmailConfirmed = user.EmailConfirmed,
                Token = string.Empty, // User doesn't carry token
            };
        }
    }
}
