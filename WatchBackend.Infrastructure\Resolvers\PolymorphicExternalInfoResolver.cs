﻿using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using WatchBackend.Core.Models;

namespace WatchBackend.Infrastructure.Resolvers
{
    public class PolymorphicExternalInfoResolver : DefaultJsonTypeInfoResolver
    {
        public override JsonTypeInfo GetTypeInfo(Type type, JsonSerializerOptions options)
        {
            var jsonTypeInfo = base.GetTypeInfo(type, options);

            Type baseFileExternalInfoType = typeof(BaseFileExternalInfo);
            if (jsonTypeInfo.Type == baseFileExternalInfoType)
            {
                jsonTypeInfo.PolymorphismOptions = new JsonPolymorphismOptions
                {
                    TypeDiscriminatorPropertyName = "$external_info_type",
                    IgnoreUnrecognizedTypeDiscriminators = true,
                    UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FailSerialization,
                    DerivedTypes =
                    {
                        new JsonDerivedType(typeof(FileMovieInfo), "movie"),
                        new JsonDerivedType(typeof(FileSeriesInfo), "series")
                    }
                };
            }

            return jsonTypeInfo;
        }
    }
}
