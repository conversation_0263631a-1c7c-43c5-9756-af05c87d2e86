﻿using System.Reflection;
using System.Text.Json;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using WatchBackend.Core.Entities;
using WatchBackend.Data.Interceptors;
using WatchBackend.Data.Models;
using WatchBackend.Data.Resolvers;

namespace WatchBackend.Data
{
    public class WatchBackendDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, int>
    {
        private readonly AuditableEntitySaveChangesInterceptor _auditableEntitySaveChangesInterceptor;
        private readonly JsonSerializerOptions _externalInfoSerializerOptions;

        public DbSet<RoomEntity> Rooms => Set<RoomEntity>();
        public DbSet<RoomUserEntity> RoomUsers => Set<RoomUserEntity>();
        public DbSet<SourceContainerPlaylistEntity> SourceContainerPlaylists =>
            Set<SourceContainerPlaylistEntity>();

        public WatchBackendDbContext(
            DbContextOptions<WatchBackendDbContext> options,
            AuditableEntitySaveChangesInterceptor auditableEntitySaveChangesInterceptor
        )
            : base(options)
        {
            _auditableEntitySaveChangesInterceptor = auditableEntitySaveChangesInterceptor;
            _externalInfoSerializerOptions = new JsonSerializerOptions
            {
                TypeInfoResolver = new PolymorphicResolver(),
            };
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            builder.Entity<SourceContainerEntity>(entity =>
            {
                entity
                    .Property(e => e.ExternalInfo)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v, _externalInfoSerializerOptions),
                        v =>
                            JsonSerializer.Deserialize<BaseExternalInfoEntity>(
                                v,
                                _externalInfoSerializerOptions
                            )
                    );
            });

            base.OnModelCreating(builder);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.AddInterceptors(_auditableEntitySaveChangesInterceptor);
        }
    }
}
