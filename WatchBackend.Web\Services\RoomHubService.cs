﻿using Mapster;
using Microsoft.AspNetCore.SignalR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Models;
using WatchBackend.Web.Hubs;

namespace WatchBackend.Web.Services
{
    public class RoomHubService : IRoomHubService
    {
        private readonly IHubContext<RoomHub> _roomHubContext;

        public RoomHubService(IHubContext<RoomHub> roomHubContext)
        {
            _roomHubContext = roomHubContext;
        }

        public async Task BroadcastRoomUserChanged(RoomUser roomUser, Room room)
        {
            var roomUserDto = roomUser.Adapt<RoomUserDto>();
            var recipients = _roomHubContext.Clients.Group(room.IdHash);

            await recipients.SendAsync("roomUserChanged", roomUserDto);
        }
    }
}
