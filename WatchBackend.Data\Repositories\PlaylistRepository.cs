﻿using Microsoft.EntityFrameworkCore;
using WatchBackend.Core.Entities;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Queries;

namespace WatchBackend.Data.Repositories
{
    public class PlaylistRepository : IPlaylistRepository
    {
        private readonly WatchBackendDbContext _context;

        public PlaylistRepository(WatchBackendDbContext context)
        {
            _context = context;
        }

        public Task<SourceContainerPlaylistEntity> GetByIdAsync(
            int id,
            PlaylistQuery? query = null,
            bool noTracking = false
        )
        {
            var playlists = GetPlaylistsQuery(id, query, noTracking);

            return playlists.FirstAsync(p => p.Id == id);
        }

        public Task<SourceContainerPlaylistEntity?> GetByIdOrDefaultAsync(
            int id,
            PlaylistQuery? query = null,
            bool noTracking = false
        )
        {
            var playlists = GetPlaylistsQuery(id, query, noTracking);

            return playlists.FirstOrDefaultAsync(p => p.Id == id);
        }

        private IQueryable<SourceContainerPlaylistEntity> GetPlaylistsQuery(
            int id,
            PlaylistQuery? query = null,
            bool noTracking = false
        )
        {
            var playlists = _context.SourceContainerPlaylists.AsQueryable();
            if (query is not null)
            {
                playlists = playlists.ApplyIncludes(query);
            }

            if (noTracking)
            {
                playlists = playlists.AsNoTrackingWithIdentityResolution();
            }

            return playlists.AsSplitQuery();
        }
    }

    internal static class QueryableSourceContainerPlaylistEntityExtensions
    {
        public static IQueryable<SourceContainerPlaylistEntity> ApplyIncludes(
            this IQueryable<SourceContainerPlaylistEntity> playlists,
            PlaylistQuery query
        )
        {
            if (query.IncludeItems)
            {
                playlists = playlists.Include(p => p.Items);

                if (query.IncludeVideo)
                {
                    playlists = playlists.Include(p => p.Items).ThenInclude(i => i.Video);
                }

                if (query.IncludeSubtitles)
                {
                    playlists = playlists.Include(p => p.Items).ThenInclude(i => i.Subtitles);
                }
            }

            return playlists;
        }
    }
}
