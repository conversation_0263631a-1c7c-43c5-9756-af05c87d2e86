﻿using FluentResults;
using WatchBackend.Core.Errors;

namespace WatchBackend.Core.Models
{
    public class Room
    {
        private List<RoomUser> _users = new();
        private Playlist<SourceContainer> _playlist = new();

        public int Id { get; set; }

        public string IdHash { get; set; } = string.Empty;

        public PlaybackState PlaybackState { get; set; }

        public DateTimeOffset PlaybackStartUtc { get; set; }

        public TimeSpan PlaybackBaseTime { get; set; }

        public TimeSpan CurrentTime
        {
            get
            {
                if (PlaybackState == PlaybackState.Playing)
                {
                    return PlaybackBaseTime + (DateTime.UtcNow - PlaybackStartUtc);
                }

                return PlaybackBaseTime;
            }
        }

        public Playlist<SourceContainer> Playlist
        {
            get { return _playlist; }
        }

        public IEnumerable<RoomUser> Users
        {
            get { return _users; }
        }

        public void AddUser(RoomUser user)
        {
            _users.Add(user);
        }

        public void RemoveUser(RoomUser user)
        {
            _users.Remove(user);
        }

        public Result SetUserRole(int userId, RoomUserRoles role)
        {
            var userResult = GetUserByUserId(userId);
            if (userResult.IsFailed)
            {
                return userResult.ToResult();
            }

            var user = userResult.Value;
            user.Role = role;

            return Result.Ok();
        }

        public Result<RoomUser> GetUserByUserId(int userId)
        {
            var roomUser = _users.Find(u => u.UserId == userId);
            if (roomUser is null)
            {
                return Result.Fail(new NotFoundError("Room user not found"));
            }

            return Result.Ok(roomUser);
        }

        public Result<SubtitleSource> SetSubtitlesOffset(string subtitlesSrc, double offset)
        {
            var subtitleSource = _playlist
                .CurrentItem?.Subtitles?.Where(s => s.Url == subtitlesSrc)
                .FirstOrDefault();
            if (subtitleSource is null)
            {
                return Result.Fail(new NotFoundError("Requested subtitles not found"));
            }

            subtitleSource.Offset = offset;

            return Result.Ok(subtitleSource);
        }

        public IEnumerable<RoomUser> GetAllConnectedUsers()
        {
            return this.Users.Where(u => u.IsConnected);
        }

        public void PlaySource()
        {
            // TODO: Source should have length, so that we know when to advance to next video etc.!

            PlaybackStartUtc = DateTime.UtcNow;
            PlaybackState = PlaybackState.Playing;
        }

        public void StopSource()
        {
            PlaybackBaseTime = TimeSpan.FromMilliseconds(0);
            PlaybackState = PlaybackState.Stopped;
        }

        public void PauseSource(TimeSpan pauseAt)
        {
            PlaybackBaseTime = pauseAt;
            PlaybackState = PlaybackState.Paused;
        }

        public void SkipSourceTo(TimeSpan skipTo)
        {
            PlaybackBaseTime = skipTo;
            PlaybackStartUtc = DateTime.UtcNow;
        }
    }

    public enum PlaybackState
    {
        Stopped = 0,
        Paused,
        Playing,
    }
}
