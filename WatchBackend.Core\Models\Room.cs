﻿using System.Diagnostics.CodeAnalysis;
using FluentResults;
using WatchBackend.Core.Errors;

namespace WatchBackend.Core.Models
{
    public class Room
    {
        private readonly List<RoomUser> _users;

        public required int Id { get; set; }

        public required string IdHash { get; set; }

        public required PlaybackState PlaybackState { get; set; }

        public required DateTimeOffset PlaybackStartUtc { get; set; }

        public required TimeSpan PlaybackBaseTime { get; set; }

        public TimeSpan CurrentTime
        {
            get
            {
                if (PlaybackState == PlaybackState.Playing)
                {
                    return PlaybackBaseTime + (DateTime.UtcNow - PlaybackStartUtc);
                }

                return PlaybackBaseTime;
            }
        }

        public required Playlist<SourceContainer> Playlist { get; init; }

        public required IEnumerable<RoomUser> Users
        {
            get => _users;
            [MemberNotNull(nameof(_users))]
            init => _users = value.ToList();
        }

        public void AddUser(RoomUser user)
        {
            _users.Add(user);
        }

        public void RemoveUser(RoomUser user)
        {
            _users.Remove(user);
        }

        public Result SetUserRole(int userId, RoomUserRoles role)
        {
            var userResult = GetUserByUserId(userId);
            if (userResult.IsFailed)
            {
                return userResult.ToResult();
            }

            var user = userResult.Value;
            user.Role = role;

            return Result.Ok();
        }

        public Result<RoomUser> GetUserByUserId(int userId)
        {
            var roomUser = _users.Find(u => u.UserId == userId);
            if (roomUser is null)
            {
                return Result.Fail(new NotFoundError("Room user not found"));
            }

            return Result.Ok(roomUser);
        }

        public Result<SubtitleSource> SetSubtitlesOffset(string subtitlesSrc, double offset)
        {
            var subtitleSource = Playlist
                .CurrentItem?.Subtitles?.Where(s => s.Url == subtitlesSrc)
                .FirstOrDefault();
            if (subtitleSource is null)
            {
                return Result.Fail(new NotFoundError("Requested subtitles not found"));
            }

            subtitleSource.Offset = offset;

            return Result.Ok(subtitleSource);
        }

        public IEnumerable<RoomUser> GetAllConnectedUsers()
        {
            return this.Users.Where(u => u.IsConnected);
        }

        public void PlaySource()
        {
            // TODO: Source should have length, so that we know when to advance to next video etc.!

            PlaybackStartUtc = DateTime.UtcNow;
            PlaybackState = PlaybackState.Playing;
        }

        public void StopSource()
        {
            PlaybackBaseTime = TimeSpan.FromMilliseconds(0);
            PlaybackState = PlaybackState.Stopped;
        }

        public void PauseSource(TimeSpan pauseAt)
        {
            PlaybackBaseTime = pauseAt;
            PlaybackState = PlaybackState.Paused;
        }

        public void SkipSourceTo(TimeSpan skipTo)
        {
            PlaybackBaseTime = skipTo;
            PlaybackStartUtc = DateTime.UtcNow;
        }

        public static Room Create()
        {
            return new Room
            {
                Id = default,
                IdHash = string.Empty,
                PlaybackState = default,
                PlaybackStartUtc = default,
                PlaybackBaseTime = default,
                Playlist = Playlist<SourceContainer>.Create(),
                Users = [],
            };
        }
    }

    public enum PlaybackState
    {
        Stopped = 0,
        Paused,
        Playing,
    }
}
