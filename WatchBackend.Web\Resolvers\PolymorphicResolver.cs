﻿using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using WatchBackend.Core.Dtos;

namespace WatchBackend.Web.Resolvers
{
    public class PolymorphicResolver : DefaultJsonTypeInfoResolver
    {
        public override JsonTypeInfo GetTypeInfo(Type type, JsonSerializerOptions options)
        {
            var jsonTypeInfo = base.GetTypeInfo(type, options);

            Type baseExternalInfoDtoType = typeof(BaseExternalInfoDto);
            Type basePlaybackStateSourceDtoType = typeof(BasePlaybackStateSourceDto);
            if (jsonTypeInfo.Type == baseExternalInfoDtoType)
            {
                jsonTypeInfo.PolymorphismOptions = new JsonPolymorphismOptions
                {
                    TypeDiscriminatorPropertyName = "$external_info_type",
                    IgnoreUnrecognizedTypeDiscriminators = true,
                    UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FailSerialization,
                    DerivedTypes =
                    {
                        new JsonDerivedType(typeof(MovieInfoDto), "movie"),
                        new JsonDerivedType(typeof(SeriesInfoDto), "series"),
                    },
                };
            }
            else if (jsonTypeInfo.Type == basePlaybackStateSourceDtoType)
            {
                jsonTypeInfo.PolymorphismOptions = new JsonPolymorphismOptions
                {
                    TypeDiscriminatorPropertyName = "type",
                    IgnoreUnrecognizedTypeDiscriminators = true,
                    UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FailSerialization,
                    DerivedTypes =
                    {
                        new JsonDerivedType(typeof(PlayPlaybackStateSourceDto), "play"),
                        new JsonDerivedType(typeof(PausePlaybackStateSourceDto), "pause"),
                        new JsonDerivedType(typeof(SeekPlaybackStateSourceDto), "seek"),
                        new JsonDerivedType(
                            typeof(SeekByKeyboardPlaybackStateSourceDto),
                            "seekByKeyboard"
                        ),
                    },
                };
            }

            return jsonTypeInfo;
        }
    }
}
