﻿using System.Net.Http.Json;
using System.Text.Json;
using FluentResults;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Mapping;
using WatchBackend.Core.Models;
using WatchBackend.Infrastructure.Policies;
using WatchBackend.Infrastructure.Resolvers;

namespace WatchBackend.Infrastructure.Services
{
    internal class SourceService : ISourceService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly JsonSerializerOptions _fileSourceContainerJsonOptions;

        public SourceService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
            _fileSourceContainerJsonOptions = new JsonSerializerOptions
            {
                TypeInfoResolver = new PolymorphicExternalInfoResolver(),
                PropertyNamingPolicy = new SnakeCaseNamingPolicy(),
            };
        }

        public async Task<Result<SourceContainer>> GetSourceFromUrlAsync(string url)
        {
            var httpClient = _httpClientFactory.CreateClient("VideoService");

            var validateUrlResult = ValidateUrl(url);
            if (validateUrlResult.IsFailed)
            {
                return validateUrlResult;
            }

            var response = await httpClient.GetAsync(url);
            if (!response.IsSuccessStatusCode)
            {
                return Result.Fail(
                    $"Failed to fetch {url} ({response.StatusCode}: {response.ReasonPhrase})"
                );
            }

            var fileSourceContainer = await response.Content.ReadFromJsonAsync<FileSourceContainer>(
                _fileSourceContainerJsonOptions
            );
            if (fileSourceContainer == null)
            {
                return Result.Fail(new InvalidError("Unsupported url"));
            }

            var baseUri = new Uri(url);
            var sourceContainer = fileSourceContainer.ToDomain(baseUri);
            EnrichSourceContainer(sourceContainer, baseUri);

            return Result.Ok(sourceContainer);
        }

        private Result ValidateUrl(string url)
        {
            try
            {
                var uri = new Uri(url);
                if (uri.Scheme != "http" && uri.Scheme != "https")
                {
                    return Result.Fail(new InvalidError("Invalid url scheme"));
                }
            }
            catch
            {
                return Result.Fail(new InvalidError("Invalid url"));
            }

            return Result.Ok();
        }

        // Add base uris to applicable source container urls
        private void EnrichSourceContainer(SourceContainer sourceContainer, Uri baseUri)
        {
            if (sourceContainer.ExternalInfo == null)
            {
                return;
            }

            if (!(sourceContainer.ExternalInfo is BaseMovieAndSeriesInfo externalInfo))
            {
                return;
            }

            if (externalInfo.Networks != null)
            {
                var networks = externalInfo.Networks.ToList();
                foreach (var network in networks)
                {
                    if (string.IsNullOrEmpty(network.LogoImageUrl))
                    {
                        continue;
                    }

                    network.LogoImageUrl = new Uri(baseUri, network.LogoImageUrl).AbsoluteUri;
                }

                externalInfo.Networks = networks;
            }

            if (externalInfo.ProductionCompanies != null)
            {
                var productionCompanies = externalInfo.ProductionCompanies.ToList();
                foreach (var productionCompany in productionCompanies)
                {
                    if (string.IsNullOrEmpty(productionCompany.LogoImageUrl))
                    {
                        continue;
                    }

                    productionCompany.LogoImageUrl = new Uri(
                        baseUri,
                        productionCompany.LogoImageUrl
                    ).AbsoluteUri;
                }

                externalInfo.ProductionCompanies = productionCompanies;
            }

            if (!string.IsNullOrEmpty(externalInfo.BackdropPlaceholderUrl))
            {
                externalInfo.BackdropPlaceholderUrl = new Uri(
                    baseUri,
                    externalInfo.BackdropPlaceholderUrl
                ).AbsoluteUri;
            }
        }
    }
}
