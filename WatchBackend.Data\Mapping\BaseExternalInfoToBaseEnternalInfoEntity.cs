﻿using Mapster;
using WatchBackend.Core.Entities;
using WatchBackend.Core.Models;

namespace WatchBackend.Data.Mapping
{
    public class BaseExternalInfoToBaseEnternalInfoEntity : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config
                .ForType<BaseExternalInfo, BaseExternalInfoEntity>()
                .Include<MovieInfo, MovieInfoEntity>()
                .Include<SeriesInfo, SeriesInfoEntity>();
        }
    }
}
