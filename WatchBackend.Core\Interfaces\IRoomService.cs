﻿using FluentResults;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Interfaces
{
    public interface IRoomService
    {
        Task<Room> CreateRoomAsync(User creatorUser);
        Task SaveRoomAsync(Room room);
        void SaveRoomDebounced(Room room);
        Task<Result<Room>> GetRoomByIdAsync(int id);
        Task<Result<Room>> GetRoomByIdHashAsync(string idHash);
        Task AddUserToRoomAsync(Room room, User user, RoomUserRoles role = RoomUserRoles.Viewer);
        Task AddSourceToPlaylist(
            Room room,
            SourceContainer source,
            bool setAsCurrentSource = false
        );

        IEnumerable<Room> GetUserConnectedRooms(User user);
        void BroadcastUserChangedToConnectedRooms(User user);
    }
}
