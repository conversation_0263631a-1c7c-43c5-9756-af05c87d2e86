﻿using Microsoft.EntityFrameworkCore;
using WatchBackend.Core.Entities;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Data.Repositories
{
    internal class RoomUserRepository : IRoomUserRepository
    {
        private readonly WatchBackendDbContext _context;

        public RoomUserRepository(WatchBackendDbContext context)
        {
            _context = context;
        }

        public void Add(RoomUserEntity user)
        {
            _context.RoomUsers.Add(user);
        }

        public Task<RoomUserEntity?> GetByIdAsync(int id)
        {
            return _context.RoomUsers.FirstOrDefaultAsync(u => u.Id == id);
        }
    }
}
