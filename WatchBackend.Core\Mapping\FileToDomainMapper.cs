﻿using WatchBackend.Core.Models;

namespace WatchBackend.Core.Mapping
{
    public static class FileToDomainMapper
    {
        public static SourceContainer ToDomain(this FileSourceContainer fileContainer, Uri baseUri)
        {
            return new SourceContainer
            {
                Id = default,
                Type = fileContainer.Type,
                Video = fileContainer.Video?.ToDomain(baseUri),
                Subtitles = fileContainer.Subtitles?.Select(s => s.ToDomain(baseUri)).ToList(),
                Attributes = null,
                ExternalInfo = fileContainer.ExternalInfo?.ToDomain(),
            };
        }

        public static VideoSource ToDomain(this FileVideoSource fileVideo, Uri baseUri)
        {
            return new VideoSource
            {
                Id = default,
                Url = new Uri(baseUri, fileVideo.Filename).AbsoluteUri,
                Attributes = new Dictionary<string, string> { { "type", fileVideo.Type } },
                Width = fileVideo.Width,
                Height = fileVideo.Height,
            };
        }

        public static SubtitleSource ToDomain(this FileSubtitleSource fileSubtitle, Uri baseUri)
        {
            return new SubtitleSource
            {
                Id = default,
                Url = new Uri(baseUri, fileSubtitle.Filename).AbsoluteUri,
                Label = fileSubtitle.Label,
                SrcLang = fileSubtitle.SrcLang,
                Offset = default,
                Attributes = null,
            };
        }

        public static BaseExternalInfo ToDomain(this BaseFileExternalInfo fileExternalInfo)
        {
            return fileExternalInfo switch
            {
                FileSeriesInfo fileSeriesInfo => fileSeriesInfo.ToDomain(),
                FileMovieInfo fileMovieInfo => fileMovieInfo.ToDomain(),
                _ => new BaseExternalInfo { Id = default, Title = fileExternalInfo.Title },
            };
        }

        public static SeriesInfo ToDomain(this FileSeriesInfo fileSeriesInfo)
        {
            return new SeriesInfo
            {
                Id = default,
                Title = fileSeriesInfo.Title,
                OriginalTitle = fileSeriesInfo.OriginalTitle,
                Description = fileSeriesInfo.Description,
                ReleaseDate = fileSeriesInfo.ReleaseDate,
                PosterImageUrl = fileSeriesInfo.PosterImageUrl,
                BackdropImageUrl = fileSeriesInfo.BackdropImageUrl,
                BackdropPlaceholderUrl = fileSeriesInfo.BackdropPlaceholderUrl,
                ImdbId = fileSeriesInfo.ImdbId,
                OriginCountries = fileSeriesInfo.OriginCountries,
                Genres = fileSeriesInfo.Genres,
                SpokenLanguages = fileSeriesInfo.SpokenLanguages,
                Networks = fileSeriesInfo.Networks?.Select(c => c.ToDomain()) ?? [],
                ProductionCompanies =
                    fileSeriesInfo.ProductionCompanies?.Select(c => c.ToDomain()) ?? [],
                SeasonNumber = fileSeriesInfo.SeasonNumber,
                EpisodeNumber = fileSeriesInfo.EpisodeNumber,
                EpisodeTitle = fileSeriesInfo.EpisodeTitle,
                Credits = fileSeriesInfo.Credits?.ToDomain() ?? SeriesCredits.Create(),
            };
        }

        public static MovieInfo ToDomain(this FileMovieInfo fileMovieInfo)
        {
            return new MovieInfo
            {
                Id = default,
                Title = fileMovieInfo.Title,
                OriginalTitle = fileMovieInfo.OriginalTitle,
                Description = fileMovieInfo.Description,
                ReleaseDate = fileMovieInfo.ReleaseDate,
                PosterImageUrl = fileMovieInfo.PosterImageUrl,
                BackdropImageUrl = fileMovieInfo.BackdropImageUrl,
                BackdropPlaceholderUrl = fileMovieInfo.BackdropPlaceholderUrl,
                ImdbId = fileMovieInfo.ImdbId,
                OriginCountries = fileMovieInfo.OriginCountries,
                Genres = fileMovieInfo.Genres,
                SpokenLanguages = fileMovieInfo.SpokenLanguages,
                Networks = [],
                ProductionCompanies =
                    fileMovieInfo.ProductionCompanies?.Select(c => c.ToDomain()) ?? [],
                Tagline = fileMovieInfo.Tagline,
                Budget = fileMovieInfo.Budget,
                Credits = fileMovieInfo.Credits?.ToDomain() ?? MovieCredits.Create(),
            };
        }

        public static SeriesCredits ToDomain(this FileSeriesCredits fileCredits)
        {
            return new SeriesCredits
            {
                Id = default,
                Actors = fileCredits.Actors?.Select(a => a.ToDomain()) ?? [],
                Writers = fileCredits.Writers?.Select(w => w.ToDomain()) ?? [],
                Directors = fileCredits.Directors?.Select(d => d.ToDomain()) ?? [],
                OriginalMusicComposers =
                    fileCredits.OriginalMusicComposers?.Select(c => c.ToDomain()) ?? [],
                GuestActors = fileCredits.GuestActors?.Select(a => a.ToDomain()) ?? [],
                Creators = fileCredits.Creators?.Select(c => c.ToDomain()) ?? [],
            };
        }

        public static MovieCredits ToDomain(this FileMovieCredits fileCredits)
        {
            return new MovieCredits
            {
                Id = default,
                Actors = fileCredits.Actors?.Select(a => a.ToDomain()) ?? [],
                Writers = fileCredits.Writers?.Select(w => w.ToDomain()) ?? [],
                Directors = fileCredits.Directors?.Select(d => d.ToDomain()) ?? [],
                OriginalMusicComposers =
                    fileCredits.OriginalMusicComposers?.Select(c => c.ToDomain()) ?? [],
            };
        }

        public static Actor ToDomain(this FileActor fileActor)
        {
            return new Actor
            {
                Id = default,
                Name = fileActor.Name,
                ProfileImageUrl = fileActor.ProfileImageUrl,
                Character = fileActor.Character,
                Order = fileActor.Order,
            };
        }

        public static Crew ToDomain(this FileCrew fileCrew)
        {
            return new Crew
            {
                Id = default,
                Name = fileCrew.Name,
                ProfileImageUrl = fileCrew.ProfileImageUrl,
            };
        }

        public static Company ToDomain(this FileCompany fileCompany)
        {
            return new Company
            {
                Id = default,
                Name = fileCompany.Name,
                LogoImageUrl = fileCompany.LogoImageUrl,
            };
        }
    }
}
