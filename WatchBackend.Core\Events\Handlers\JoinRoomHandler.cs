﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    internal class JoinRoomHandler : IRequestHandler<JoinRoomCommand, Result<JoinRoomResponseDto>>
    {
        private readonly IRoomService _roomService;
        private readonly IUserService _userService;

        public JoinRoomHandler(IRoomService roomService, IUserService userService)
        {
            _roomService = roomService;
            _userService = userService;
        }

        public async Task<Result<JoinRoomResponseDto>> Handle(
            JoinRoomCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var roomResult = await _roomService.GetRoomByIdHashAsync(request.RoomIdHash);
                if (roomResult.IsFailed)
                {
                    return roomResult.ToResult<JoinRoomResponseDto>();
                }

                var userResult = await _userService.GetUserById(request.UserId);
                if (userResult.IsFailed)
                {
                    if (userResult.HasError<NotFoundError>())
                    {
                        return Result.Fail(new UnauthorizedError(userResult.Errors[0].Message));
                    }

                    return roomResult.ToResult<JoinRoomResponseDto>();
                }

                var room = roomResult.Value;
                var user = userResult.Value;
                await _roomService.AddUserToRoomAsync(room, user);

                var response = new JoinRoomResponseDto();
                return Result.Ok(response);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
