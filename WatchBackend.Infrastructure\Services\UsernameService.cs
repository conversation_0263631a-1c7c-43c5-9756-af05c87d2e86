﻿using Syllabore;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Infrastructure.Services
{
    internal class UsernameService : IUsernameService
    {
        private readonly NameGenerator _nameGenerator;

        public UsernameService()
        {
            _nameGenerator = new NameGenerator()
            {
                MinimumSyllables = 2,
                MaximumSyllables = 2,
                Provider = new SyllableGenerator()
                    .WithLeadingConsonants("bcdfgjklmnprstvzhr")
                    .WithLeadingConsonantSequences("st", "ph", "br")
                    .WithVowels("aeiou")
                    .WithFinalConsonants("dlmnrstfgkpv")
                    .WithFinalConsonantSequences("st", "rn", "lt")
                    .WithProbability(
                        (GeneratorProbabilityBuilder x) =>
                            x.OfLeadingConsonants(0.95, 0.25).OfFinalConsonants(0.5, 0.25)
                    ),
            };

            var a = _nameGenerator;
        }

        public string GetRandomUsername()
        {
            return _nameGenerator.Next();
        }
    }
}
