using WatchBackend.Core.Entities;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Mapping
{
    public static class DomainToEntityMapper
    {
        public static RoomEntity ToEntity(this Room room)
        {
            return new RoomEntity
            {
                IdHash = room.IdHash,
                Users = room.Users.Select(u => u.ToEntity()).ToList(),
                PlaybackState = (Entities.PlaybackState)room.PlaybackState,
                CurrentTime = room.CurrentTime,
                Playlist = room.Playlist.ToEntity(),
            };
        }

        public static RoomUserEntity ToEntity(this RoomUser roomUser)
        {
            return new RoomUserEntity
            {
                UserId = roomUser.UserId,
                Username = roomUser.Username,
                Role = (RoomUserEntityRoles)roomUser.Role,
            };
        }

        public static SourceContainerPlaylistEntity ToEntity(
            this Playlist<SourceContainer> playlist
        )
        {
            return new SourceContainerPlaylistEntity
            {
                CurrentIndex = playlist.CurrentIndex,
                Items = playlist.Items.Select(item => item.ToEntity()).ToList(),
            };
        }

        public static SourceContainerEntity ToEntity(this SourceContainer sourceContainer)
        {
            return new SourceContainerEntity
            {
                Type = sourceContainer.Type,
                Video = sourceContainer.Video?.ToEntity(),
                Subtitles = sourceContainer.Subtitles?.Select(s => s.ToEntity()).ToList(),
                Attributes = sourceContainer.Attributes,
                ExternalInfo = sourceContainer.ExternalInfo?.ToEntity(),
            };
        }

        public static VideoSourceEntity ToEntity(this VideoSource videoSource)
        {
            return new VideoSourceEntity
            {
                Url = videoSource.Url,
                Attributes = videoSource.Attributes,
                Width = videoSource.Width,
                Height = videoSource.Height,
            };
        }

        public static SubtitleSourceEntity ToEntity(this SubtitleSource subtitleSource)
        {
            return new SubtitleSourceEntity
            {
                Url = subtitleSource.Url,
                Label = subtitleSource.Label,
                SrcLang = subtitleSource.SrcLang,
                Offset = subtitleSource.Offset,
                Attributes = subtitleSource.Attributes,
            };
        }

        public static BaseExternalInfoEntity ToEntity(this BaseExternalInfo externalInfo)
        {
            if (externalInfo is BaseMovieAndSeriesInfo baseMovieSeries)
            {
                var baseEntity = baseMovieSeries.ToEntity();

                // Map derived types
                switch (externalInfo)
                {
                    case SeriesInfo seriesInfo:
                        var seriesEntity = seriesInfo.ToEntity();
                        // Copy base properties
                        CopyBaseMovieAndSeriesInfo(baseEntity, seriesEntity);
                        return seriesEntity;
                    case MovieInfo movieInfo:
                        var movieEntity = movieInfo.ToEntity();
                        // Copy base properties
                        CopyBaseMovieAndSeriesInfo(baseEntity, movieEntity);
                        return movieEntity;
                    default:
                        return baseEntity;
                }
            }

            // Fallback for other types
            return new BaseExternalInfoEntity { Title = externalInfo.Title };
        }

        // Helper to copy base properties from BaseMovieAndSeriesInfoEntity to derived entities
        private static void CopyBaseMovieAndSeriesInfo(
            BaseMovieAndSeriesInfoEntity source,
            BaseMovieAndSeriesInfoEntity target
        )
        {
            target.Title = source.Title;
            target.OriginalTitle = source.OriginalTitle;
            target.Description = source.Description;
            target.ReleaseDate = source.ReleaseDate;
            target.PosterImageUrl = source.PosterImageUrl;
            target.BackdropImageUrl = source.BackdropImageUrl;
            target.BackdropPlaceholderUrl = source.BackdropPlaceholderUrl;
            target.ImdbId = source.ImdbId;
            target.OriginCountries = source.OriginCountries;
            target.Genres = source.Genres;
            target.SpokenLanguages = source.SpokenLanguages;
            target.ProductionCompanies = source.ProductionCompanies;
        }

        public static BaseMovieAndSeriesInfoEntity ToEntity(this BaseMovieAndSeriesInfo info)
        {
            return new BaseMovieAndSeriesInfoEntity
            {
                Title = info.Title,
                OriginalTitle = info.OriginalTitle,
                Description = info.Description,
                ReleaseDate = info.ReleaseDate,
                PosterImageUrl = info.PosterImageUrl,
                BackdropImageUrl = info.BackdropImageUrl,
                BackdropPlaceholderUrl = info.BackdropPlaceholderUrl,
                ImdbId = info.ImdbId,
                OriginCountries = info.OriginCountries?.ToList(),
                Genres = info.Genres?.ToList(),
                SpokenLanguages = info.SpokenLanguages?.ToList(),
                ProductionCompanies = info.ProductionCompanies?.Select(c => c.ToEntity()).ToList(),
            };
        }

        public static SeriesInfoEntity ToEntity(this SeriesInfo seriesInfo)
        {
            return new SeriesInfoEntity
            {
                Title = seriesInfo.Title,
                OriginalTitle = seriesInfo.OriginalTitle,
                Description = seriesInfo.Description,
                ReleaseDate = seriesInfo.ReleaseDate,
                PosterImageUrl = seriesInfo.PosterImageUrl,
                BackdropImageUrl = seriesInfo.BackdropImageUrl,
                BackdropPlaceholderUrl = seriesInfo.BackdropPlaceholderUrl,
                ImdbId = seriesInfo.ImdbId,
                OriginCountries = seriesInfo.OriginCountries?.ToList(),
                Genres = seriesInfo.Genres?.ToList(),
                SpokenLanguages = seriesInfo.SpokenLanguages?.ToList(),
                ProductionCompanies = seriesInfo
                    .ProductionCompanies?.Select(c => c.ToEntity())
                    .ToList(),
                Networks = seriesInfo.Networks?.Select(c => c.ToEntity()).ToList(),
                SeasonNumber = seriesInfo.SeasonNumber,
                EpisodeNumber = seriesInfo.EpisodeNumber,
                EpisodeTitle = seriesInfo.EpisodeTitle,
                Credits = seriesInfo.Credits?.ToEntity(),
            };
        }

        public static MovieInfoEntity ToEntity(this MovieInfo movieInfo)
        {
            return new MovieInfoEntity
            {
                Title = movieInfo.Title,
                OriginalTitle = movieInfo.OriginalTitle,
                Description = movieInfo.Description,
                ReleaseDate = movieInfo.ReleaseDate,
                PosterImageUrl = movieInfo.PosterImageUrl,
                BackdropImageUrl = movieInfo.BackdropImageUrl,
                BackdropPlaceholderUrl = movieInfo.BackdropPlaceholderUrl,
                ImdbId = movieInfo.ImdbId,
                OriginCountries = movieInfo.OriginCountries?.ToList(),
                Genres = movieInfo.Genres?.ToList(),
                SpokenLanguages = movieInfo.SpokenLanguages?.ToList(),
                ProductionCompanies = movieInfo
                    .ProductionCompanies?.Select(c => c.ToEntity())
                    .ToList(),
                Tagline = movieInfo.Tagline,
                Budget = movieInfo.Budget,
                Credits = movieInfo.Credits?.ToEntity(),
            };
        }

        public static SeriesCreditsEntity ToEntity(this SeriesCredits credits)
        {
            return new SeriesCreditsEntity
            {
                // Base properties from BaseMovieAndSeriesCredits
                Actors = credits.Actors?.Select(a => a.ToEntity()).ToList(),
                Writers = credits.Writers?.Select(c => c.ToEntity()).ToList(),
                Directors = credits.Directors?.Select(c => c.ToEntity()).ToList(),
                OriginalMusicComposers = credits.OriginalMusicComposers?.Select(c => c.ToEntity()).ToList(),
                // SeriesCredits specific properties
                GuestActors = credits.GuestActors?.Select(a => a.ToEntity()).ToList(),
                Creators = credits.Creators?.Select(c => c.ToEntity()).ToList(),
            };
        }

        public static MovieCreditsEntity ToEntity(this MovieCredits credits)
        {
            return new MovieCreditsEntity
            {
                // Base properties from BaseMovieAndSeriesCredits
                Actors = credits.Actors?.Select(a => a.ToEntity()).ToList(),
                Writers = credits.Writers?.Select(c => c.ToEntity()).ToList(),
                Directors = credits.Directors?.Select(c => c.ToEntity()).ToList(),
                OriginalMusicComposers = credits.OriginalMusicComposers?.Select(c => c.ToEntity()).ToList(),
            };
        }

        public static ActorEntity ToEntity(this Actor actor)
        {
            return new ActorEntity
            {
                Name = actor.Name,
                ProfileImageUrl = actor.ProfileImageUrl,
                Character = actor.Character,
                Order = actor.Order,
            };
        }

        public static CrewEntity ToEntity(this Crew crew)
        {
            return new CrewEntity { Name = crew.Name, ProfileImageUrl = crew.ProfileImageUrl };
        }

        public static CompanyEntity ToEntity(this Company company)
        {
            return new CompanyEntity
            {
                // Map all properties of Company to CompanyEntity here
                // Example:
                Name = company.Name,
                LogoPath = company.LogoPath,
                OriginCountry = company.OriginCountry,
            };
        }
    }
}
