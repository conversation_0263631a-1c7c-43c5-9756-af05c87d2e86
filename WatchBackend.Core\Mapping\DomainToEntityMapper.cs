using WatchBackend.Core.Entities;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Mapping
{
    public static class DomainToEntityMapper
    {
        public static RoomEntity ToEntity(this Room room)
        {
            return new RoomEntity
            {
                IdHash = room.IdHash,
                Users = room.Users.Select(u => u.ToEntity()).ToList(),
                PlaybackState = (Entities.PlaybackState)room.PlaybackState,
                CurrentTime = room.CurrentTime,
                Playlist = room.Playlist.ToEntity(),
            };
        }

        public static RoomUserEntity ToEntity(this RoomUser roomUser)
        {
            return new RoomUserEntity
            {
                UserId = roomUser.UserId,
                Username = roomUser.Username,
                Role = (RoomUserEntityRoles)roomUser.Role,
            };
        }

        public static SourceContainerPlaylistEntity ToEntity(
            this Playlist<SourceContainer> playlist
        )
        {
            return new SourceContainerPlaylistEntity
            {
                CurrentIndex = playlist.CurrentIndex,
                Items = playlist.Items.Select(item => item.ToEntity()).ToList(),
            };
        }

        public static SourceContainerEntity ToEntity(this SourceContainer sourceContainer)
        {
            return new SourceContainerEntity
            {
                Type = sourceContainer.Type,
                Video = sourceContainer.Video?.ToEntity(),
                Subtitles = sourceContainer.Subtitles?.Select(s => s.ToEntity()).ToList(),
                Attributes = sourceContainer.Attributes,
                ExternalInfo = sourceContainer.ExternalInfo?.ToEntity(),
            };
        }

        public static VideoSourceEntity ToEntity(this VideoSource videoSource)
        {
            return new VideoSourceEntity
            {
                Url = videoSource.Url,
                Attributes = videoSource.Attributes,
                Width = videoSource.Width,
                Height = videoSource.Height,
            };
        }

        public static SubtitleSourceEntity ToEntity(this SubtitleSource subtitleSource)
        {
            return new SubtitleSourceEntity
            {
                Url = subtitleSource.Url,
                Label = subtitleSource.Label,
                SrcLang = subtitleSource.SrcLang,
                Offset = subtitleSource.Offset,
                Attributes = subtitleSource.Attributes,
            };
        }

        public static BaseExternalInfoEntity ToEntity(this BaseExternalInfo externalInfo)
        {
            switch (externalInfo)
            {
                case SeriesInfo seriesInfo:
                    var seriesEntity = seriesInfo.ToEntity();
                    return seriesEntity;
                case MovieInfo movieInfo:
                    var movieEntity = movieInfo.ToEntity();
                    return movieEntity;
                default:
                    throw new NotSupportedException("Unsupported external info type");
            }
        }

        public static SeriesInfoEntity ToEntity(this SeriesInfo seriesInfo)
        {
            return new SeriesInfoEntity
            {
                Title = seriesInfo.Title,
                OriginalTitle = seriesInfo.OriginalTitle,
                Description = seriesInfo.Description,
                ReleaseDate = seriesInfo.ReleaseDate,
                PosterImageUrl = seriesInfo.PosterImageUrl,
                BackdropImageUrl = seriesInfo.BackdropImageUrl,
                BackdropPlaceholderUrl = seriesInfo.BackdropPlaceholderUrl,
                ImdbId = seriesInfo.ImdbId,
                OriginCountries = seriesInfo.OriginCountries?.ToList(),
                Genres = seriesInfo.Genres?.ToList(),
                SpokenLanguages = seriesInfo.SpokenLanguages?.ToList(),
                ProductionCompanies = seriesInfo
                    .ProductionCompanies?.Select(c => c.ToEntity())
                    .ToList(),
                Networks = seriesInfo.Networks?.Select(c => c.ToEntity()).ToList(),
                SeasonNumber = seriesInfo.SeasonNumber,
                EpisodeNumber = seriesInfo.EpisodeNumber,
                EpisodeTitle = seriesInfo.EpisodeTitle,
                Credits = seriesInfo.Credits?.ToEntity(),
            };
        }

        public static MovieInfoEntity ToEntity(this MovieInfo movieInfo)
        {
            return new MovieInfoEntity
            {
                Title = movieInfo.Title,
                OriginalTitle = movieInfo.OriginalTitle,
                Description = movieInfo.Description,
                ReleaseDate = movieInfo.ReleaseDate,
                PosterImageUrl = movieInfo.PosterImageUrl,
                BackdropImageUrl = movieInfo.BackdropImageUrl,
                BackdropPlaceholderUrl = movieInfo.BackdropPlaceholderUrl,
                ImdbId = movieInfo.ImdbId,
                OriginCountries = movieInfo.OriginCountries?.ToList(),
                Genres = movieInfo.Genres?.ToList(),
                SpokenLanguages = movieInfo.SpokenLanguages?.ToList(),
                ProductionCompanies = movieInfo
                    .ProductionCompanies?.Select(c => c.ToEntity())
                    .ToList(),
                Tagline = movieInfo.Tagline,
                Budget = movieInfo.Budget,
                Credits = movieInfo.Credits?.ToEntity(),
            };
        }

        public static SeriesCreditsEntity ToEntity(this SeriesCredits credits)
        {
            return new SeriesCreditsEntity
            {
                Actors = credits.Actors?.Select(a => a.ToEntity()).ToList(),
                Writers = credits.Writers?.Select(w => w.ToEntity()).ToList(),
                Directors = credits.Directors?.Select(d => d.ToEntity()).ToList(),
                OriginalMusicComposers = credits.OriginalMusicComposers?.Select(c => c.ToEntity()).ToList(),
                // SeriesCredits specific properties
                GuestActors = credits.GuestActors?.Select(a => a.ToEntity()).ToList(),
                Creators = credits.Creators?.Select(c => c.ToEntity()).ToList(),
            };
        }

        public static MovieCreditsEntity ToEntity(this MovieCredits credits)
        {
            return new MovieCreditsEntity
            {
                // Base properties from BaseMovieAndSeriesCredits
                Actors = credits.Actors?.Select(a => a.ToEntity()).ToList(),
                Writers = credits.Writers?.Select(w => w.ToEntity()).ToList(),
                Directors = credits.Directors?.Select(d => d.ToEntity()).ToList(),
                OriginalMusicComposers = credits.OriginalMusicComposers?.Select(c => c.ToEntity()).ToList(),
            };
        }

        public static ActorEntity ToEntity(this Actor actor)
        {
            return new ActorEntity
            {
                Name = actor.Name,
                ProfileImageUrl = actor.ProfileImageUrl,
                Character = actor.Character,
                Order = actor.Order,
            };
        }

        public static CrewEntity ToEntity(this Crew crew)
        {
            return new CrewEntity { Name = crew.Name, ProfileImageUrl = crew.ProfileImageUrl };
        }

        public static CompanyEntity ToEntity(this Company company)
        {
            return new CompanyEntity
            {
                Name = company.Name,
                LogoImageUrl = company.LogoImageUrl,
            };
        }
    }
}
