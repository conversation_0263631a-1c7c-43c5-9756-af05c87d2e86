﻿using Mapster;
using WatchBackend.Core.Models;

namespace WatchBackend.Infrastructure.Mapping
{
    internal class FileCompanyToCompany : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config
                .ForType<FileCompany, Company>()
                .Map(dst => dst.Name, src => src.Name)
                .Map(dst => dst.LogoImageUrl, src => src.LogoImageUrl);
        }
    }
}
