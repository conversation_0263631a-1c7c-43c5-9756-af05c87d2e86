﻿using FluentResults;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Interfaces
{
    public interface IUserService
    {
        Task<Result<User>> CreateUser();
        Task<Result<User>> GetUserById(int id);
        Task<Result<User>> GetUserByEmail(string email);
        Task<Result<User>> RerollUsername(int id);
        Task<Result<User>> ChangeUsername(int id, string username);
        Task<Result<User>> SetEmailAsync(int id, string email, bool isConfirmed = false);
        Task<bool> IsUsernameAvailableAsync(string username);

        // Checks if the provided email address is available. You can pass an allowed
        // user id which would be allowed to have the provided email address and still
        // count as available.
        // This is used in cases where a user didn't receive their confirmation email
        // and they want it to be resent.
        Task<bool> IsEmailAvailableAsync(string email, int allowedUserId);
        Task<Result<string>> GetJwtTokenAsync(User user);
        Task<Result<string>> GenerateConfirmationTokenAsync(User user);
        Task<Result<bool>> VerifyConfirmationTokenAsync(int id, string token);
        Task<Result<string>> GenerateRecoveryTokenAsync(User user);
        Task<Result<bool>> VerifyRecoveryTokenAsync(int id, string token);
    }
}
