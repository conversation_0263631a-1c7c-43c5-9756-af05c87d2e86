﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <LangVersion>12.0</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="7.0.20" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.20">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.1" />
    <PackageReference Include="Syllabore" Version="2.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WatchBackend.Core\WatchBackend.Core.csproj" />
    <ProjectReference Include="..\WatchBackend.Data\WatchBackend.Data.csproj" />
    <ProjectReference Include="..\WatchBackend.Infrastructure\WatchBackend.Infrastructure.csproj" />
  </ItemGroup>

</Project>
