﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    public class VerifyRecoveryTokenHandler
        : IRequestHandler<VerifyRecoveryTokenCommand, Result<VerifyRecoveryTokenResponseDto>>
    {
        private readonly IUserService _userService;

        public VerifyRecoveryTokenHandler(IUserService userService)
        {
            _userService = userService;
        }

        public async Task<Result<VerifyRecoveryTokenResponseDto>> Handle(
            VerifyRecoveryTokenCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                if (string.IsNullOrEmpty(request.Token))
                {
                    return Result.Fail(new InvalidError("No token provided"));
                }

                var verifyResult = await _userService.VerifyRecoveryTokenAsync(
                    request.Id,
                    request.Token
                );
                if (verifyResult.IsFailed)
                {
                    return verifyResult.ToResult<VerifyRecoveryTokenResponseDto>();
                }

                var isTokenVerified = verifyResult.Value;
                if (!isTokenVerified)
                {
                    return Result.Fail(new InvalidError("Token not valid"));
                }

                var userResult = await _userService.GetUserById(request.Id);
                if (userResult.IsFailed)
                {
                    return userResult.ToResult<VerifyRecoveryTokenResponseDto>();
                }

                var user = userResult.Value;
                var tokenResult = await _userService.GetJwtTokenAsync(user);
                if (tokenResult.IsFailed)
                {
                    return tokenResult.ToResult<VerifyRecoveryTokenResponseDto>();
                }

                var token = tokenResult.Value;
                var userDto = new VerifyRecoveryTokenResponseDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    EmailConfirmed = user.EmailConfirmed,
                    Token = token,
                };

                return Result.Ok(userDto);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
