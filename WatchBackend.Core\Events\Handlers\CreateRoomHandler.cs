﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    public class CreateRoomHandler
        : IRequestHandler<CreateRoomCommand, Result<CreateRoomResponseDto>>
    {
        private readonly IRoomService _roomService;
        private readonly IUserService _userService;

        public CreateRoomHandler(IRoomService roomService, IUserService userService)
        {
            _roomService = roomService;
            _userService = userService;
        }

        public async Task<Result<CreateRoomResponseDto>> Handle(
            CreateRoomCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var userResult = await _userService.GetUserById(request.UserId);
                if (userResult.IsFailed)
                {
                    if (userResult.HasError<NotFoundError>())
                    {
                        return Result.Fail(new UnauthorizedError(userResult.Errors[0].Message));
                    }

                    return userResult.ToResult<CreateRoomResponseDto>();
                }

                var user = userResult.Value;
                var room = await _roomService.CreateRoomAsync(user);
                var createRoomResponseDto = new CreateRoomResponseDto { IdHash = room.IdHash };

                return Result.Ok(createRoomResponseDto);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
