﻿namespace WatchBackend.Core.Extensions
{
    public static class IReadonlyListExtensions
    {
        public static int IndexOf<TItem>(this IReadOnlyList<TItem> list, TItem item)
            where TItem : class
        {
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i] == item)
                {
                    return i;
                }
            }

            return -1;
        }
    }
}
