﻿using FluentResults;
using HashidsNet;
using Microsoft.Extensions.Options;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Interfaces;
using WatchBackend.Infrastructure.Models;

namespace WatchBackend.Infrastructure.Services
{
    internal class IdHashService : IIdHashService
    {
        private readonly Hashids _hashids;

        public IdHashService(IOptions<HashIdsOptions> options)
        {
            _hashids = new Hashids(
                salt: options.Value.Salt,
                minHashLength: options.Value.MinHashLength,
                alphabet: options.Value.Alphabet
            );
        }

        public Result<int> HashToId(string hash)
        {
            if (_hashids.TryDecodeSingle(hash, out var id))
            {
                return Result.Ok(id);
            }

            return Result.Fail(new InvalidError("Failed to process hash"));
        }

        public string IdToHash(int id)
        {
            return _hashids.Encode(id);
        }
    }
}
