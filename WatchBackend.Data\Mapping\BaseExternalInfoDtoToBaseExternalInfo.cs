﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Mapster;
using WatchBackend.Core.Entities;
using WatchBackend.Core.Models;

namespace WatchBackend.Data.Mapping
{
    public class BaseExternalInfoDtoToBaseExternalInfo : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config
                .ForType<BaseExternalInfoEntity, BaseExternalInfo>()
                .Include<MovieInfoEntity, MovieInfo>()
                .Include<SeriesInfoEntity, SeriesInfo>();
        }
    }
}
