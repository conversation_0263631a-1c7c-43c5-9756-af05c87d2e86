﻿namespace WatchBackend.Core.Models
{
    public class RoomUser
    {
        private readonly HashSet<string> _connectionIds = new();
        private readonly object _lock = new();

        public int Id { get; set; }
        public int UserId { get; set; }
        public IReadOnlySet<string> ConnectionIds
        {
            get { return _connectionIds; }
        }
        public bool IsConnected
        {
            get { return _connectionIds.Count > 0; }
        }

        public string Username { get; set; } = string.Empty;
        public RoomUserRoles Role { get; set; }

        public void AddConnectionId(string connectionId)
        {
            lock (_lock)
            {
                _connectionIds.Add(connectionId);
            }
        }

        public void RemoveConnectionId(string connectionId)
        {
            lock (_lock)
            {
                _connectionIds.Remove(connectionId);
            }
        }
    }

    public enum RoomUserRoles
    {
        Guest = 0,
        Viewer,
        Moderator,
        Administrator,
        Creator
    }
}
