﻿namespace WatchBackend.Core.Models
{
    public class RoomUser
    {
        private readonly HashSet<string> _connectionIds = new();
        private readonly object _lock = new();

        public required int Id { get; set; }
        public required int UserId { get; set; }
        public IReadOnlySet<string> ConnectionIds
        {
            get { return _connectionIds; }
        }
        public bool IsConnected
        {
            get { return _connectionIds.Count > 0; }
        }

        public required string Username { get; set; }
        public required RoomUserRoles Role { get; set; }

        public void AddConnectionId(string connectionId)
        {
            lock (_lock)
            {
                _connectionIds.Add(connectionId);
            }
        }

        public void RemoveConnectionId(string connectionId)
        {
            lock (_lock)
            {
                _connectionIds.Remove(connectionId);
            }
        }
    }

    public enum RoomUserRoles
    {
        Guest = 0,
        Viewer,
        Moderator,
        Administrator,
        Creator,
    }
}
