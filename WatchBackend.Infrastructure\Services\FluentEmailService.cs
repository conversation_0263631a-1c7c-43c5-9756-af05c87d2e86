﻿using System.Web;
using FluentEmail.Core;
using FluentResults;
using Microsoft.Extensions.Options;
using WatchBackend.Core.Interfaces;
using WatchBackend.Infrastructure.Models;

namespace WatchBackend.Infrastructure.Services
{
    internal class FluentEmailService : IEmailService
    {
        private const string CONFIRMATION_TEMPLATE = "ConfirmationEmail.html";
        private const string RECOVERY_TEMPLATE = "RecoveryEmail.html";
        private const string ID_QUERY = "id";
        private const string TOKEN_QUERY = "token";
        private const string USERNAME_QUERY = "username";

        private readonly EmailOptions _options;
        private readonly IFluentEmail _fluentEmail;

        public FluentEmailService(IFluentEmail fluentEmail, IOptions<EmailOptions> options)
        {
            _options = options.Value;
            _fluentEmail = fluentEmail;
        }

        public async Task<Result> SendConfirmationEmail(
            int id,
            string userEmailAddress,
            string confirmationToken,
            string desiredUsername
        )
        {
            var uriBuilder = new UriBuilder(_options.ConfirmationBaseHref);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);

            query[ID_QUERY] = id.ToString();
            query[TOKEN_QUERY] = confirmationToken;
            query[USERNAME_QUERY] = desiredUsername;

            uriBuilder.Query = query.ToString();
            var confirmationHref = uriBuilder.ToString();

            var email = _fluentEmail
                .To(userEmailAddress)
                .Subject(_options.ConfirmationSubject)
                .UsingBootstrapTemplateFromEmbedded(
                    CONFIRMATION_TEMPLATE,
                    new
                    {
                        HeaderImgSrc = _options.HeaderImgSrc,
                        UserEmailAddress = userEmailAddress,
                        WatchHref = _options.WatchHref,
                        ConfirmationHref = confirmationHref,
                    }
                );

            var result = await email.SendAsync();
            if (result == null)
            {
                return Result.Fail(
                    new ExceptionalError(
                        new InvalidOperationException("Sending email failed with no result")
                    )
                );
            }

            if (!result.Successful)
            {
                return Result.Fail(
                    new ExceptionalError(
                        new InvalidOperationException(string.Join("; ", result.ErrorMessages))
                    )
                );
            }

            return Result.Ok();
        }

        public async Task<Result> SendRecoveryEmail(
            int id,
            string username,
            string emailAddress,
            string recoveryToken
        )
        {
            var uriBuilder = new UriBuilder(_options.RecoveryBaseHref);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);

            query[ID_QUERY] = id.ToString();
            query[TOKEN_QUERY] = recoveryToken;

            uriBuilder.Query = query.ToString();
            var recoveryHref = uriBuilder.ToString();

            var email = _fluentEmail
                .To(emailAddress)
                .Subject(_options.RecoverySubject)
                .UsingBootstrapTemplateFromEmbedded(
                    RECOVERY_TEMPLATE,
                    new
                    {
                        HeaderImgSrc = _options.HeaderImgSrc,
                        Username = username,
                        WatchHref = _options.WatchHref,
                        RecoveryHref = recoveryHref,
                    }
                );

            var result = await email.SendAsync();
            if (result == null)
            {
                return Result.Fail(
                    new ExceptionalError(
                        new InvalidOperationException("Sending email failed with no result")
                    )
                );
            }

            if (!result.Successful)
            {
                return Result.Fail(
                    new ExceptionalError(
                        new InvalidOperationException(string.Join("; ", result.ErrorMessages))
                    )
                );
            }

            return Result.Ok();
        }
    }
}
