﻿namespace WatchBackend.Infrastructure.Models
{
    internal class EmailOptions
    {
        public string ConfirmationSubject { get; set; } = string.Empty;
        public string ConfirmationBaseHref { get; set; } = string.Empty;
        public string RecoverySubject { get; set; } = string.Empty;
        public string RecoveryBaseHref { get; set; } = string.Empty;
        public string HeaderImgSrc { get; set; } = string.Empty;
        public string WatchHref { get; set; } = string.Empty;
    }
}
