﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;

namespace WatchBackend.Core.Events.Commands
{
    public class JoinRoomCommand : IRequest<Result<JoinRoomResponseDto>>
    {
        public string RoomIdHash { get; init; }
        public int UserId { get; init; }

        public JoinRoomCommand(string roomIdHash, int userId)
        {
            RoomIdHash = roomIdHash;
            UserId = userId;
        }
    }
}
