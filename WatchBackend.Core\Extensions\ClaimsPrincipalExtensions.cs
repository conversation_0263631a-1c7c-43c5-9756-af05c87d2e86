﻿using FluentResults;
using System.Security.Claims;
using WatchBackend.Core.Errors;

namespace WatchBackend.Core.Extensions
{
    public static class ClaimsPrincipalExtensions
    {
        public static Result<int> GetClaimAsInt(
            this ClaimsPrincipal claimsPrincipal,
            string claimType
        )
        {
            var strValue = claimsPrincipal.FindFirst(claimType)?.Value;
            if (int.TryParse(strValue, out int value))
            {
                return Result.Ok(value);
            }

            return Result.Fail(new InvalidError("Invalid value"));
        }
    }
}
