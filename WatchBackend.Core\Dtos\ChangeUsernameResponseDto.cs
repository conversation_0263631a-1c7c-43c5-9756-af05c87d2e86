﻿using WatchBackend.Core.Models;

namespace WatchBackend.Core.Dtos
{
    public class ChangeUsernameResponseDto
    {
        public ChangeUsernameStatus Status { get; set; }
        public GetUserResponseDto? User { get; set; }
    }

    public enum ChangeUsernameStatus
    {
        Unchanged = 0,
        Changed,
        EmailConfirmationRequired,
        EmailAlreadyTaken,
        UsernameAlreadyTaken,
    }
}
