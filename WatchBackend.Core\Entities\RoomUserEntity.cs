﻿namespace WatchBackend.Core.Entities
{
    public class RoomUserEntity : BaseAuditableEntity
    {
        public required int UserId { get; set; }
        public required string Username { get; set; }
        public required RoomUserEntityRoles Role { get; set; }
    }

    public enum RoomUserEntityRoles
    {
        Guest = 0,
        Viewer,
        Moderator,
        Administrator,
        Creator,
    }
}
