﻿using WatchBackend.Core.Interfaces;

namespace WatchBackend.Data
{
    internal class UnitOfWork : IUnitOfWork
    {
        private readonly WatchBackendDbContext _context;

        public IRoomRepository Rooms { get; init; }
        public IRoomUserRepository RoomUsers { get; init; }
        public IPlaylistRepository Playlists { get; init; }

        public UnitOfWork(
            WatchBackendDbContext context,
            IRoomRepository roomRepository,
            IRoomUserRepository roomsUserRepository,
            IPlaylistRepository playlistRepository
        )
        {
            _context = context;
            Rooms = roomRepository;
            RoomUsers = roomsUserRepository;
            Playlists = playlistRepository;
        }

        public Task CompleteAsync()
        {
            return _context.SaveChangesAsync();
        }
    }
}
