﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    public class RequestRecoverUserHandler
        : IRequestHandler<RecoverUserCommand, Result<RecoverUserResponseDto>>
    {
        private readonly IUserService _userService;
        private readonly IEmailService _emailService;

        public RequestRecoverUserHandler(IUserService userService, IEmailService emailService)
        {
            _userService = userService;
            _emailService = emailService;
        }

        public async Task<Result<RecoverUserResponseDto>> Handle(
            RecoverUserCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var trimmedEmail = request.Email;
                if (string.IsNullOrWhiteSpace(trimmedEmail))
                {
                    return Result.Fail(new InvalidError("Email is not valid"));
                }

                var userResult = await _userService.GetUserByEmail(trimmedEmail);
                if (userResult.IsFailed)
                {
                    return Result.Fail(new NotFoundError("No user registered with provided email"));
                }

                var user = userResult.Value;
                if (!user.EmailConfirmed)
                {
                    return Result.Fail(new InvalidError("Email is not confirmed"));
                }

                var recoveryTokenResult = await _userService.GenerateRecoveryTokenAsync(user);
                if (recoveryTokenResult.IsFailed)
                {
                    return recoveryTokenResult.ToResult<RecoverUserResponseDto>();
                }

                var recoveryToken = recoveryTokenResult.Value;
                var emailResult = await _emailService.SendRecoveryEmail(
                    user.Id,
                    user.Username,
                    trimmedEmail,
                    recoveryToken
                );
                if (emailResult.IsFailed)
                {
                    return emailResult.ToResult<RecoverUserResponseDto>();
                }

                var recoveryResponse = new RecoverUserResponseDto();
                return Result.Ok(recoveryResponse);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
