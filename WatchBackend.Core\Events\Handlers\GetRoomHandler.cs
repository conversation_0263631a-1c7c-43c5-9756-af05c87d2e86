﻿using FluentResults;
using Mapster;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Events.Queries;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    public class GetRoomHandler : IRequestHandler<GetRoomQuery, Result<GetRoomResponseDto>>
    {
        private readonly IRoomService _roomService;

        public GetRoomHandler(IRoomService roomService)
        {
            this._roomService = roomService;
        }

        public async Task<Result<GetRoomResponseDto>> Handle(
            GetRoomQuery request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var roomResult = await _roomService.GetRoomByIdHashAsync(request.RoomIdHash);
                if (roomResult.IsFailed)
                {
                    return roomResult.ToResult<GetRoomResponseDto>();
                }

                var room = roomResult.Value;
                var roomDto = room.Adapt<GetRoomResponseDto>();

                return Result.Ok(roomDto);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
