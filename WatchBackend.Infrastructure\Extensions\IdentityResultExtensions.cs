﻿using FluentResults;
using Microsoft.AspNetCore.Identity;
using WatchBackend.Core.Models;

namespace WatchBackend.Infrastructure.Extensions
{
    public static class IdentityResultExtensions
    {
        public static Result<User> MapToUserResult(this IdentityResult identityResult, User user)
        {
            if (!identityResult.Succeeded)
            {
                var errors = identityResult.Errors.Select(
                    e => new Error(e.Description).WithMetadata("ErrorCode", e.Code)
                );

                return Result.Fail(errors);
            }

            return Result.Ok(user);
        }
    }
}
