namespace WatchBackend.Core.Interfaces
{
    /// <summary>
    /// Service for debouncing operations to prevent excessive execution of expensive operations
    /// </summary>
    public interface IDebounceService
    {
        /// <summary>
        /// Debounces an async operation by key. If called multiple times with the same key,
        /// only the last operation will be executed after the specified delay.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        /// <param name="operation">The async operation to execute</param>
        /// <param name="delay">Delay before executing the operation</param>
        void DebounceAsync(string key, Func<Task> operation, TimeSpan delay);

        /// <summary>
        /// Debounces an async operation by key with a default delay.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        /// <param name="operation">The async operation to execute</param>
        void DebounceAsync(string key, Func<Task> operation);

        /// <summary>
        /// Debounces an async operation that is executed inside a fresh DI scope when it fires.
        /// Use this for operations that need scoped services (e.g., DbContext, repositories).
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        /// <param name="operation">The async operation to execute with a scoped IServiceProvider</param>
        /// <param name="delay">Delay before executing the operation</param>
        void DebounceAsync(string key, Func<IServiceProvider, Task> operation, TimeSpan delay);

        /// <summary>
        /// Debounces an async operation by key with a default delay that runs inside a fresh DI scope.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        /// <param name="operation">The async operation to execute with a scoped IServiceProvider</param>
        void DebounceAsync(string key, Func<IServiceProvider, Task> operation);

        /// <summary>
        /// Debounces an async operation for a typed service. The service is resolved in a fresh DI scope
        /// when the debounce fires.
        /// </summary>
        /// <typeparam name="TService">The service type to resolve</typeparam>
        /// <param name="key">Unique identifier for the operation</param>
        /// <param name="operation">The async operation to execute with the resolved service</param>
        /// <param name="delay">Delay before executing the operation</param>
        void DebounceAsync<TService>(string key, Func<TService, Task> operation, TimeSpan delay) where TService : notnull;

        /// <summary>
        /// Debounces an async operation for a typed service with the default delay.
        /// </summary>
        /// <typeparam name="TService">The service type to resolve</typeparam>
        /// <param name="key">Unique identifier for the operation</param>
        /// <param name="operation">The async operation to execute with the resolved service</param>
        void DebounceAsync<TService>(string key, Func<TService, Task> operation) where TService : notnull;

        /// <summary>
        /// Immediately executes any pending operation for the given key and cancels the debounce timer.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        Task FlushAsync(string key);

        /// <summary>
        /// Cancels any pending operation for the given key without executing it.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        void Cancel(string key);

        /// <summary>
        /// Cancels all pending operations without executing them.
        /// </summary>
        void CancelAll();
    }
}
