﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    public class VerifyEmailHandler
        : IRequestHandler<VerifyEmailCommand, Result<VerifyEmailResponseDto>>
    {
        private readonly IUserService _userService;

        public VerifyEmailHandler(IUserService userService)
        {
            _userService = userService;
        }

        public async Task<Result<VerifyEmailResponseDto>> Handle(
            VerifyEmailCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                if (string.IsNullOrEmpty(request.Token))
                {
                    return Result.Fail(new InvalidError("No token provided"));
                }

                var verifyResult = await _userService.VerifyConfirmationTokenAsync(
                    request.Id,
                    request.Token
                );
                if (verifyResult.IsFailed)
                {
                    return verifyResult.ToResult<VerifyEmailResponseDto>();
                }

                var isTokenVerified = verifyResult.Value;
                if (!isTokenVerified)
                {
                    return Result.Fail(new InvalidError("Token not valid"));
                }

                var userResult = await _userService.GetUserById(request.Id);
                if (userResult.IsFailed)
                {
                    return userResult.ToResult<VerifyEmailResponseDto>();
                }

                var user = userResult.Value;
                var tokenResult = await _userService.GetJwtTokenAsync(user);
                if (tokenResult.IsFailed)
                {
                    return tokenResult.ToResult<VerifyEmailResponseDto>();
                }

                var token = tokenResult.Value;
                var userDto = new VerifyEmailResponseDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    EmailConfirmed = user.EmailConfirmed,
                    Token = token,
                };

                return Result.Ok(userDto);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
