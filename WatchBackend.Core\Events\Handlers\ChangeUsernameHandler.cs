﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Mapping;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Events.Handlers
{
    public class ChangeUsernameHandler
        : IRequestHandler<ChangeUsernameCommand, Result<ChangeUsernameResponseDto>>
    {
        private const int USERNAME_MAX_LENGTH = 16;

        private readonly IUserService _userService;
        private readonly IRoomService _roomService;
        private readonly IEmailService _emailService;

        public ChangeUsernameHandler(
            IUserService userService,
            IRoomService roomService,
            IEmailService emailService
        )
        {
            _userService = userService;
            _roomService = roomService;
            _emailService = emailService;
        }

        public async Task<Result<ChangeUsernameResponseDto>> Handle(
            ChangeUsernameCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var userResult = await _userService.GetUserById(request.UserId);
                if (userResult.IsFailed)
                {
                    return userResult.ToResult<ChangeUsernameResponseDto>();
                }

                var trimmedUsername = request.Username.Trim();
                if (string.IsNullOrWhiteSpace(trimmedUsername))
                {
                    return Result.Fail(new InvalidError("Username is invalid"));
                }

                if (trimmedUsername.Length > USERNAME_MAX_LENGTH)
                {
                    return Result.Fail(
                        new InvalidError(
                            $"Username can't be longer than {USERNAME_MAX_LENGTH} characters"
                        )
                    );
                }

                if (!trimmedUsername.All(c => char.IsLetterOrDigit(c)))
                {
                    return Result.Fail(
                        new InvalidError("Username can only contain latin letters and digits")
                    );
                }

                var user = userResult.Value;
                if (!user.EmailConfirmed && string.IsNullOrWhiteSpace(request.Email))
                {
                    return Result.Fail(new InvalidError("Email is invalid"));
                }

                if (user.EmailConfirmed)
                {
                    if (user.Username == trimmedUsername)
                    {
                        var unchangedResponseResult = await GetResponseDtoAsync(
                            ChangeUsernameStatus.Unchanged,
                            user
                        );
                        if (unchangedResponseResult.IsFailed)
                        {
                            return unchangedResponseResult.ToResult<ChangeUsernameResponseDto>();
                        }

                        var unchangedResponse = unchangedResponseResult.Value;
                        return Result.Ok(unchangedResponse);
                    }

                    var isUsernameAvailable = await _userService.IsUsernameAvailableAsync(
                        trimmedUsername
                    );
                    if (!isUsernameAvailable)
                    {
                        var usernameTakenResponse = new ChangeUsernameResponseDto
                        {
                            Status = ChangeUsernameStatus.UsernameAlreadyTaken,
                        };

                        return Result.Ok(usernameTakenResponse);
                    }

                    var changeUsernameResult = await _userService.ChangeUsername(
                        user.Id,
                        trimmedUsername
                    );
                    if (changeUsernameResult.IsFailed)
                    {
                        return changeUsernameResult.ToResult<ChangeUsernameResponseDto>();
                    }

                    user = changeUsernameResult.Value;
                    _roomService.BroadcastUserChangedToConnectedRooms(user);

                    var changedResponseResult = await GetResponseDtoAsync(
                        ChangeUsernameStatus.Changed,
                        user
                    );
                    if (changedResponseResult.IsFailed)
                    {
                        return changedResponseResult.ToResult<ChangeUsernameResponseDto>();
                    }

                    var changedResponse = changedResponseResult.Value;
                    return Result.Ok(changedResponse);
                }

                var isEmailAvailable = await _userService.IsEmailAvailableAsync(
                    request.Email,
                    user.Id
                );
                if (!isEmailAvailable)
                {
                    var emailUnavailableResponse = new ChangeUsernameResponseDto
                    {
                        Status = ChangeUsernameStatus.EmailAlreadyTaken,
                    };

                    return emailUnavailableResponse;
                }

                var setEmailResult = await _userService.SetEmailAsync(user.Id, request.Email);
                if (setEmailResult.IsFailed)
                {
                    return setEmailResult.ToResult<ChangeUsernameResponseDto>();
                }

                var confirmationTokenResult = await _userService.GenerateConfirmationTokenAsync(
                    user
                );
                if (confirmationTokenResult.IsFailed)
                {
                    return confirmationTokenResult.ToResult<ChangeUsernameResponseDto>();
                }

                var confirmationToken = confirmationTokenResult.Value;
                var emailResult = await _emailService.SendConfirmationEmail(
                    user.Id,
                    request.Email,
                    confirmationToken,
                    trimmedUsername
                );
                if (emailResult.IsFailed)
                {
                    return emailResult.ToResult<ChangeUsernameResponseDto>();
                }

                user = setEmailResult.Value;
                var confirmationResponseResult = await GetResponseDtoAsync(
                    ChangeUsernameStatus.EmailConfirmationRequired,
                    user
                );
                if (confirmationResponseResult.IsFailed)
                {
                    return confirmationResponseResult.ToResult<ChangeUsernameResponseDto>();
                }

                var confirmationResponse = confirmationResponseResult.Value;
                return Result.Ok(confirmationResponse);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }

        private async Task<Result<ChangeUsernameResponseDto>> GetResponseDtoAsync(
            ChangeUsernameStatus status,
            User? user
        )
        {
            GetUserResponseDto? userResponseDto = null;
            if (user != null)
            {
                var userTokenResult = await _userService.GetJwtTokenAsync(user);
                if (userTokenResult.IsFailed)
                {
                    return userTokenResult.ToResult<ChangeUsernameResponseDto>();
                }

                var userToken = userTokenResult.Value;
                userResponseDto = user.ToGetUserResponseDto();
                userResponseDto.Token = userToken;
            }

            var responseDto = new ChangeUsernameResponseDto
            {
                Status = status,
                User = userResponseDto,
            };

            return Result.Ok(responseDto);
        }
    }
}
