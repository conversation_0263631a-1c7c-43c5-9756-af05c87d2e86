﻿namespace WatchBackend.Core.Entities
{
    public class SourceContainerEntity : BaseAuditableEntity
    {
        public required string Type { get; set; }
        public required VideoSourceEntity? Video { get; set; }
        public required IList<SubtitleSourceEntity>? Subtitles { get; set; }
        public required IDictionary<string, string>? Attributes { get; set; }
        public required BaseExternalInfoEntity? ExternalInfo { get; set; }
    }

    public class VideoSourceEntity : BaseAuditableEntity
    {
        public required string Url { get; set; }
        public required IDictionary<string, string>? Attributes { get; set; }
        public required int? Width { get; set; }
        public required int? Height { get; set; }
    }

    public class SubtitleSourceEntity : BaseAuditableEntity
    {
        public required string Url { get; init; }
        public required string Label { get; init; }
        public required string SrcLang { get; init; }
        public required double Offset { get; set; }
        public required IDictionary<string, string>? Attributes { get; init; }
    }

    public class BaseExternalInfoEntity : BaseAuditableEntity
    {
        public required string? Title { get; set; }
    }

    public class BaseMovieAndSeriesInfoEntity : BaseExternalInfoEntity
    {
        public required string? OriginalTitle { get; set; }
        public required string? Description { get; set; }
        public required DateOnly? ReleaseDate { get; set; }
        public required string? PosterImageUrl { get; set; }
        public required string? BackdropImageUrl { get; set; }
        public required string? BackdropPlaceholderUrl { get; set; }
        public required string? ImdbId { get; set; }
        public required ICollection<string>? OriginCountries { get; set; }
        public required ICollection<string>? Genres { get; set; }
        public required ICollection<string>? SpokenLanguages { get; set; }
        public required ICollection<CompanyEntity>? ProductionCompanies { get; set; }
    }

    public class BaseMovieAndSeriesCreditsEntity : BaseAuditableEntity
    {
        public required ICollection<ActorEntity>? Actors { get; set; }
        public required ICollection<CrewEntity>? Writers { get; set; }
        public required ICollection<CrewEntity>? Directors { get; set; }
        public required ICollection<CrewEntity>? OriginalMusicComposers { get; set; }
    }

    public class SeriesInfoEntity : BaseMovieAndSeriesInfoEntity
    {
        public required int? SeasonNumber { get; set; }
        public required int? EpisodeNumber { get; set; }
        public required string? EpisodeTitle { get; set; }
        public required ICollection<CompanyEntity>? Networks { get; set; }
        public required SeriesCreditsEntity? Credits { get; set; }
    }

    public class SeriesCreditsEntity : BaseMovieAndSeriesCreditsEntity
    {
        public required ICollection<ActorEntity>? GuestActors { get; set; }
        public required ICollection<CrewEntity>? Creators { get; set; }
    }

    public class MovieInfoEntity : BaseMovieAndSeriesInfoEntity
    {
        public required string? Tagline { get; set; }
        public required uint? Budget { get; set; }
        public required MovieCreditsEntity? Credits { get; set; }
    }

    public class MovieCreditsEntity : BaseMovieAndSeriesCreditsEntity { }

    public class CompanyEntity : BaseAuditableEntity
    {
        public required string? Name { get; set; }
        public required string? LogoImageUrl { get; set; }
    }

    public class CrewEntity : BaseAuditableEntity
    {
        public required string? Name { get; set; }
        public required string? ProfileImageUrl { get; set; }
    }

    public class ActorEntity : CrewEntity
    {
        public required string? Character { get; set; }
        public required int? Order { get; set; }
    }
}
