﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WatchBackend.Data;

#nullable disable

namespace WatchBackend.Data.Migrations
{
    [DbContext(typeof(WatchBackendDbContext))]
    [Migration("20241011111243_AddSourceContainerExternalInfo")]
    partial class AddSourceContainerExternalInfo
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.10");

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.BaseExternalInfoEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("$external_info_type")
                        .IsRequired()
                        .HasMaxLength(34)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("BaseExternalInfoEntity");

                    b.HasDiscriminator<string>("$external_info_type").HasValue("BaseExternalInfoEntity");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.CompanyEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("LogoImageUrl")
                        .HasColumnType("TEXT");

                    b.Property<int?>("MovieInfoEntityId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<int?>("SeriesInfoEntityId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SeriesInfoEntityId1")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("MovieInfoEntityId");

                    b.HasIndex("SeriesInfoEntityId");

                    b.HasIndex("SeriesInfoEntityId1");

                    b.ToTable("CompanyEntity");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.CrewEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("MovieCreditsEntityId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("MovieCreditsEntityId1")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("MovieCreditsEntityId2")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProfileImageUrl")
                        .HasColumnType("TEXT");

                    b.Property<int?>("SeriesCreditsEntityId2")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SeriesCreditsEntityId3")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SeriesCreditsEntityId4")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SeriesCreditsEntityId5")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("MovieCreditsEntityId");

                    b.HasIndex("MovieCreditsEntityId1");

                    b.HasIndex("MovieCreditsEntityId2");

                    b.HasIndex("SeriesCreditsEntityId2");

                    b.HasIndex("SeriesCreditsEntityId3");

                    b.HasIndex("SeriesCreditsEntityId4");

                    b.HasIndex("SeriesCreditsEntityId5");

                    b.ToTable("CrewEntity");

                    b.HasDiscriminator().HasValue("CrewEntity");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.MovieCreditsEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("MovieCreditsEntity");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.RoomEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<TimeSpan>("CurrentTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("IdHash")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("PlaybackState")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PlaylistId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("PlaylistId");

                    b.ToTable("Rooms");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.RoomUserEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("Role")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("RoomEntityId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoomEntityId");

                    b.ToTable("RoomUsers");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SeriesCreditsEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("SeriesCreditsEntity");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SourceContainerEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Attributes")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ExternalInfoId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("SourceContainerPlaylistEntityId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("VideoId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ExternalInfoId");

                    b.HasIndex("SourceContainerPlaylistEntityId");

                    b.HasIndex("VideoId");

                    b.ToTable("SourceContainerEntity");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SourceContainerPlaylistEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<int>("CurrentIndex")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("SourceContainerPlaylists");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SubtitleSourceEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Attributes")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("SourceContainerEntityId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SrcLang")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("SourceContainerEntityId");

                    b.ToTable("SubtitleSourceEntity");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.VideoSourceEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Attributes")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Created")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("VideoSourceEntity");
                });

            modelBuilder.Entity("WatchBackend.Data.Models.ApplicationRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("WatchBackend.Data.Models.ApplicationUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("TEXT");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.MovieInfoEntity", b =>
                {
                    b.HasBaseType("WatchBackend.Core.Entities.BaseExternalInfoEntity");

                    b.Property<string>("BackdropImageUrl")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<uint?>("Budget")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CreditsId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("Genres")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("ImdbId")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("OriginCountries")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("OriginalTitle")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("PosterImageUrl")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<DateOnly?>("ReleaseDate")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("SpokenLanguages")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("Tagline")
                        .HasColumnType("TEXT");

                    b.HasIndex("CreditsId");

                    b.HasDiscriminator().HasValue("movie");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SeriesInfoEntity", b =>
                {
                    b.HasBaseType("WatchBackend.Core.Entities.BaseExternalInfoEntity");

                    b.Property<string>("BackdropImageUrl")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<int?>("CreditsId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<int?>("EpisodeNumber")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EpisodeTitle")
                        .HasColumnType("TEXT");

                    b.Property<string>("Genres")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("ImdbId")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("OriginCountries")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("OriginalTitle")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<string>("PosterImageUrl")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<DateOnly?>("ReleaseDate")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.Property<int?>("SeasonNumber")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SpokenLanguages")
                        .ValueGeneratedOnUpdateSometimes()
                        .HasColumnType("TEXT");

                    b.HasIndex("CreditsId");

                    b.ToTable("BaseExternalInfoEntity", t =>
                        {
                            t.Property("CreditsId")
                                .HasColumnName("SeriesInfoEntity_CreditsId");
                        });

                    b.HasDiscriminator().HasValue("series");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.ActorEntity", b =>
                {
                    b.HasBaseType("WatchBackend.Core.Entities.CrewEntity");

                    b.Property<string>("Character")
                        .HasColumnType("TEXT");

                    b.Property<int?>("MovieCreditsEntityId3")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("Order")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SeriesCreditsEntityId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SeriesCreditsEntityId1")
                        .HasColumnType("INTEGER");

                    b.HasIndex("MovieCreditsEntityId3");

                    b.HasIndex("SeriesCreditsEntityId");

                    b.HasIndex("SeriesCreditsEntityId1");

                    b.HasDiscriminator().HasValue("ActorEntity");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("WatchBackend.Data.Models.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("WatchBackend.Data.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("WatchBackend.Data.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("WatchBackend.Data.Models.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WatchBackend.Data.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("WatchBackend.Data.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.CompanyEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.MovieInfoEntity", null)
                        .WithMany("ProductionCompanies")
                        .HasForeignKey("MovieInfoEntityId");

                    b.HasOne("WatchBackend.Core.Entities.SeriesInfoEntity", null)
                        .WithMany("Networks")
                        .HasForeignKey("SeriesInfoEntityId");

                    b.HasOne("WatchBackend.Core.Entities.SeriesInfoEntity", null)
                        .WithMany("ProductionCompanies")
                        .HasForeignKey("SeriesInfoEntityId1");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.CrewEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.MovieCreditsEntity", null)
                        .WithMany("Directors")
                        .HasForeignKey("MovieCreditsEntityId");

                    b.HasOne("WatchBackend.Core.Entities.MovieCreditsEntity", null)
                        .WithMany("OriginalMusicComposers")
                        .HasForeignKey("MovieCreditsEntityId1");

                    b.HasOne("WatchBackend.Core.Entities.MovieCreditsEntity", null)
                        .WithMany("Writers")
                        .HasForeignKey("MovieCreditsEntityId2");

                    b.HasOne("WatchBackend.Core.Entities.SeriesCreditsEntity", null)
                        .WithMany("Creators")
                        .HasForeignKey("SeriesCreditsEntityId2");

                    b.HasOne("WatchBackend.Core.Entities.SeriesCreditsEntity", null)
                        .WithMany("Directors")
                        .HasForeignKey("SeriesCreditsEntityId3");

                    b.HasOne("WatchBackend.Core.Entities.SeriesCreditsEntity", null)
                        .WithMany("OriginalMusicComposers")
                        .HasForeignKey("SeriesCreditsEntityId4");

                    b.HasOne("WatchBackend.Core.Entities.SeriesCreditsEntity", null)
                        .WithMany("Writers")
                        .HasForeignKey("SeriesCreditsEntityId5");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.RoomEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.SourceContainerPlaylistEntity", "Playlist")
                        .WithMany()
                        .HasForeignKey("PlaylistId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Playlist");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.RoomUserEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.RoomEntity", null)
                        .WithMany("Users")
                        .HasForeignKey("RoomEntityId");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SourceContainerEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.BaseExternalInfoEntity", "ExternalInfo")
                        .WithMany()
                        .HasForeignKey("ExternalInfoId");

                    b.HasOne("WatchBackend.Core.Entities.SourceContainerPlaylistEntity", null)
                        .WithMany("Items")
                        .HasForeignKey("SourceContainerPlaylistEntityId");

                    b.HasOne("WatchBackend.Core.Entities.VideoSourceEntity", "Video")
                        .WithMany()
                        .HasForeignKey("VideoId");

                    b.Navigation("ExternalInfo");

                    b.Navigation("Video");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SubtitleSourceEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.SourceContainerEntity", null)
                        .WithMany("Subtitles")
                        .HasForeignKey("SourceContainerEntityId");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.MovieInfoEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.MovieCreditsEntity", "Credits")
                        .WithMany()
                        .HasForeignKey("CreditsId");

                    b.Navigation("Credits");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SeriesInfoEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.SeriesCreditsEntity", "Credits")
                        .WithMany()
                        .HasForeignKey("CreditsId");

                    b.Navigation("Credits");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.ActorEntity", b =>
                {
                    b.HasOne("WatchBackend.Core.Entities.MovieCreditsEntity", null)
                        .WithMany("Actors")
                        .HasForeignKey("MovieCreditsEntityId3");

                    b.HasOne("WatchBackend.Core.Entities.SeriesCreditsEntity", null)
                        .WithMany("Actors")
                        .HasForeignKey("SeriesCreditsEntityId");

                    b.HasOne("WatchBackend.Core.Entities.SeriesCreditsEntity", null)
                        .WithMany("GuestActors")
                        .HasForeignKey("SeriesCreditsEntityId1");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.MovieCreditsEntity", b =>
                {
                    b.Navigation("Actors");

                    b.Navigation("Directors");

                    b.Navigation("OriginalMusicComposers");

                    b.Navigation("Writers");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.RoomEntity", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SeriesCreditsEntity", b =>
                {
                    b.Navigation("Actors");

                    b.Navigation("Creators");

                    b.Navigation("Directors");

                    b.Navigation("GuestActors");

                    b.Navigation("OriginalMusicComposers");

                    b.Navigation("Writers");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SourceContainerEntity", b =>
                {
                    b.Navigation("Subtitles");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SourceContainerPlaylistEntity", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.MovieInfoEntity", b =>
                {
                    b.Navigation("ProductionCompanies");
                });

            modelBuilder.Entity("WatchBackend.Core.Entities.SeriesInfoEntity", b =>
                {
                    b.Navigation("Networks");

                    b.Navigation("ProductionCompanies");
                });
#pragma warning restore 612, 618
        }
    }
}
