﻿namespace WatchBackend.Core.Models
{
    public class SourceContainer
    {
        public int Id { get; set; }
        public string Type { get; init; } = string.Empty;
        public VideoSource? Video { get; init; }
        public IList<SubtitleSource>? Subtitles { get; init; }
        public IDictionary<string, string>? Attributes { get; init; }
        public BaseExternalInfo? ExternalInfo { get; init; }
    }

    public class VideoSource
    {
        public int Id { get; set; }
        public string Url { get; init; } = string.Empty;
        public IDictionary<string, string>? Attributes { get; init; }
        public int? Width { get; init; }
        public int? Height { get; init; }
    }

    public class SubtitleSource
    {
        public int Id { get; set; }
        public string Url { get; init; } = string.Empty;
        public string Label { get; init; } = string.Empty;
        public string SrcLang { get; init; } = string.Empty;
        public double Offset { get; set; } = 0;
        public IDictionary<string, string>? Attributes { get; init; }
    }

    public class BaseExternalInfo
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
    }

    public class BaseMovieAndSeriesInfo : BaseExternalInfo
    {
        public string OriginalTitle { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateOnly ReleaseDate { get; set; }
        public string PosterImageUrl { get; set; } = string.Empty;
        public string BackdropImageUrl { get; set; } = string.Empty;
        public string BackdropPlaceholderUrl { get; set; } = string.Empty;
        public string ImdbId { get; set; } = string.Empty;
        public IEnumerable<string> OriginCountries { get; set; } = [];
        public IEnumerable<string> Genres { get; set; } = [];
        public IEnumerable<string> SpokenLanguages { get; set; } = [];
        public IEnumerable<Company> Networks { get; set; } = [];
        public IEnumerable<Company> ProductionCompanies { get; set; } = [];
    }

    public class BaseMovieAndSeriesCredits
    {
        public int Id { get; set; }
        public IEnumerable<Actor> Actors { get; set; } = [];
        public IEnumerable<Crew> Writers { get; set; } = [];
        public IEnumerable<Crew> Directors { get; set; } = [];
        public IEnumerable<Crew> OriginalMusicComposers { get; set; } = [];
    }

    public class SeriesInfo : BaseMovieAndSeriesInfo
    {
        public int SeasonNumber { get; set; }
        public int EpisodeNumber { get; set; }
        public string EpisodeTitle { get; set; } = string.Empty;
        public SeriesCredits Credits { get; set; } = new SeriesCredits();
    }

    public class SeriesCredits : BaseMovieAndSeriesCredits
    {
        public IEnumerable<Actor> GuestActors { get; set; } = [];
        public IEnumerable<Crew> Creators { get; set; } = [];
    }

    public class MovieInfo : BaseMovieAndSeriesInfo
    {
        public string Tagline { get; set; } = string.Empty;
        public uint Budget { get; set; }
        public MovieCredits Credits { get; set; } = new MovieCredits();
    }

    public class MovieCredits : BaseMovieAndSeriesCredits { }

    public class Company
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string LogoImageUrl { get; set; } = string.Empty;
    }

    public class Crew
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string ProfileImageUrl { get; set; } = string.Empty;
    }

    public class Actor : Crew
    {
        public string Character { get; set; } = string.Empty;
        public int Order { get; set; }
    }
}
