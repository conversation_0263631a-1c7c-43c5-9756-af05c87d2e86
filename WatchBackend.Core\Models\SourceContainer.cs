﻿namespace WatchBackend.Core.Models
{
    public class SourceContainer
    {
        public required int Id { get; set; }
        public required string Type { get; init; }
        public required VideoSource? Video { get; init; }
        public required IList<SubtitleSource>? Subtitles { get; init; }
        public required IDictionary<string, string>? Attributes { get; init; }
        public required BaseExternalInfo? ExternalInfo { get; init; }
    }

    public class VideoSource
    {
        public required int Id { get; set; }
        public required string Url { get; init; }
        public required IDictionary<string, string>? Attributes { get; init; }
        public required int? Width { get; init; }
        public required int? Height { get; init; }
    }

    public class SubtitleSource
    {
        public required int Id { get; set; }
        public required string Url { get; init; }
        public required string Label { get; init; }
        public required string SrcLang { get; init; }
        public required double Offset { get; set; }
        public required IDictionary<string, string>? Attributes { get; init; }
    }

    public class BaseExternalInfo
    {
        public required int Id { get; set; }
        public required string Title { get; set; }
    }

    public class BaseMovieAndSeriesInfo : BaseExternalInfo
    {
        public required string OriginalTitle { get; set; }
        public required string Description { get; set; }
        public required DateOnly ReleaseDate { get; set; }
        public required string PosterImageUrl { get; set; }
        public required string BackdropImageUrl { get; set; }
        public required string BackdropPlaceholderUrl { get; set; }
        public required string ImdbId { get; set; }
        public required IEnumerable<string> OriginCountries { get; set; }
        public required IEnumerable<string> Genres { get; set; }
        public required IEnumerable<string> SpokenLanguages { get; set; }
        public required IEnumerable<Company> Networks { get; set; }
        public required IEnumerable<Company> ProductionCompanies { get; set; }
    }

    public class BaseMovieAndSeriesCredits
    {
        public required int Id { get; set; }
        public required IEnumerable<Actor> Actors { get; set; }
        public required IEnumerable<Crew> Writers { get; set; }
        public required IEnumerable<Crew> Directors { get; set; }
        public required IEnumerable<Crew> OriginalMusicComposers { get; set; }
    }

    public class SeriesInfo : BaseMovieAndSeriesInfo
    {
        public required int SeasonNumber { get; set; }
        public required int EpisodeNumber { get; set; }
        public required string EpisodeTitle { get; set; }
        public required SeriesCredits Credits { get; set; }
    }

    public class SeriesCredits : BaseMovieAndSeriesCredits
    {
        public required IEnumerable<Actor> GuestActors { get; set; }
        public required IEnumerable<Crew> Creators { get; set; }

        public static SeriesCredits Create()
        {
            return new SeriesCredits
            {
                Id = default,
                Actors = [],
                Writers = [],
                Directors = [],
                OriginalMusicComposers = [],
                GuestActors = [],
                Creators = [],
            };
        }
    }

    public class MovieInfo : BaseMovieAndSeriesInfo
    {
        public required string Tagline { get; set; }
        public required uint Budget { get; set; }
        public required MovieCredits Credits { get; set; }
    }

    public class MovieCredits : BaseMovieAndSeriesCredits
    {
        public static MovieCredits Create()
        {
            return new MovieCredits
            {
                Id = default,
                Actors = [],
                Writers = [],
                Directors = [],
                OriginalMusicComposers = [],
            };
        }
    }

    public class Company
    {
        public required int Id { get; set; }
        public required string Name { get; set; }
        public required string LogoImageUrl { get; set; }
    }

    public class Crew
    {
        public required int Id { get; set; }
        public required string Name { get; set; }
        public required string ProfileImageUrl { get; set; }
    }

    public class Actor : Crew
    {
        public required string Character { get; set; }
        public required int Order { get; set; }
    }
}
