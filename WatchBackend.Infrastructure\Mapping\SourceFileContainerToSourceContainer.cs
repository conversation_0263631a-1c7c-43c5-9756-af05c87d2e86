﻿using Mapster;
using WatchBackend.Core.Models;

namespace WatchBackend.Infrastructure.Mapping
{
    public class FileSourceContainerToSourceContainer : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config
                .ForType<(FileSourceContainer, Uri), SourceContainer>()
                .Map(dst => dst.Type, src => src.Item1.Type)
                .Map(
                    dst => dst.Video,
                    src => new VideoSource
                    {
                        Id = default,
                        Url = new Uri(src.Item2, src.Item1.Video.Filename).AbsoluteUri,
                        Attributes = new Dictionary<string, string>()
                        {
                            { "type", src.Item1.Video.Type },
                        },
                        Width = src.Item1.Video.Width,
                        Height = src.Item1.Video.Height,
                    }
                )
                .Map(
                    dst => dst.Subtitles,
                    src =>
                        src.Item1.Subtitles.Select(s => new SubtitleSource
                        {
                            Id = default,
                            Offset = default,
                            Attributes = default,
                            Url = new Uri(src.Item2, s.Filename).AbsoluteUri,
                            Label = s.Label,
                            SrcLang = s.SrcLang,
                        })
                )
                .Map(dst => dst.ExternalInfo, src => src.Item1.ExternalInfo);
        }
    }
}
