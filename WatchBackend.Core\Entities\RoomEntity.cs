﻿namespace WatchBackend.Core.Entities
{
    public class RoomEntity : BaseAuditableEntity
    {
        public required string IdHash { get; set; }
        public required List<RoomUserEntity> Users { get; set; } = [];
        public required PlaybackState PlaybackState { get; set; }
        public required TimeSpan CurrentTime { get; set; }
        public required SourceContainerPlaylistEntity Playlist { get; set; }
    }

    public enum PlaybackState
    {
        Stopped = 0,
        Paused,
        Playing,
    }
}
