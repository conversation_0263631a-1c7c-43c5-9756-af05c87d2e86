﻿using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WatchBackend.Infrastructure.Models;

namespace WatchBackend.Infrastructure.Providers
{
    internal class RecoveryTokenProvider<TUser> : DataProtectorTokenProvider<TUser>
        where TUser : IdentityUser<int>
    {
        public RecoveryTokenProvider(
            IDataProtectionProvider dataProtectionProvider,
            IOptions<ConfirmationTokenProviderOptions> options,
            ILogger<ConfirmationTokenProvider<TUser>> logger
        )
            : base(dataProtectionProvider, options, logger) { }
    }
}
