﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Core.Events.Handlers
{
    internal class RerollUsernameHandler
        : IRequestHandler<RerollUsernameCommand, Result<RerollUsernameResponseDto>>
    {
        private readonly IUserService _userService;
        private readonly IRoomService _roomService;
        private readonly IRoomHubService _roomHubService;

        public RerollUsernameHandler(
            IUserService userService,
            IRoomService roomService,
            IRoomHubService roomHubService
        )
        {
            _userService = userService;
            _roomService = roomService;
            _roomHubService = roomHubService;
        }

        public async Task<Result<RerollUsernameResponseDto>> Handle(
            RerollUsernameCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                var rerollUsernameResult = await _userService.RerollUsername(request.UserId);
                if (rerollUsernameResult.IsFailed)
                {
                    return rerollUsernameResult.ToResult<RerollUsernameResponseDto>();
                }

                var user = rerollUsernameResult.Value;
                var tokenResult = await _userService.GetJwtTokenAsync(user);
                if (tokenResult.IsFailed)
                {
                    return tokenResult.ToResult<RerollUsernameResponseDto>();
                }

                var token = tokenResult.Value;
                var rerollUsernameResponseDto = new RerollUsernameResponseDto
                {
                    Username = user.Username,
                    Token = token,
                };

                _roomService.BroadcastUserChangedToConnectedRooms(user);

                return Result.Ok(rerollUsernameResponseDto);
            }
            catch (Exception exception)
            {
                return Result.Fail(new ExceptionalError(exception));
            }
        }
    }
}
