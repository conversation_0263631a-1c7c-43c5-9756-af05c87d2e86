﻿using System.Security.Claims;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Events.Queries;
using WatchBackend.Core.Extensions;
using WatchBackend.Core.Requests;
using WatchBackend.Web.Extensions;

namespace WatchBackend.Web.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<UsersController> _logger;

        public UsersController(IMediator mediator, ILogger<UsersController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> CreateUser()
        {
            var command = new CreateUserCommand();
            var result = await _mediator.Send(command);

            return result.ToActionResult(this);
        }

        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetUser()
        {
            var userIdResult = this.User.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult.IsFailed)
            {
                return userIdResult.ToActionResult(this);
            }

            var userId = userIdResult.Value;
            var query = new GetUserQuery { UserId = userId };
            var result = await _mediator.Send(query);

            return result.ToActionResult(this);
        }

        [HttpPost("{id}/username/reroll")]
        [Authorize]
        public async Task<IActionResult> RerollUsername(int id)
        {
            var userIdResult = this.User.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult.IsFailed)
            {
                return userIdResult.ToActionResult(this);
            }

            var userId = userIdResult.Value;
            if (userId != id)
            {
                return Result
                    .Fail(
                        new UnauthorizedError("Provided user id doesn't match authorized user's id")
                    )
                    .ToUnauthorizedActionResult(this);
            }

            var command = new RerollUsernameCommand(userId);
            var result = await _mediator.Send(command);

            return result.ToActionResult(this);
        }

        [HttpPatch("{id}/username")]
        [Authorize]
        public async Task<IActionResult> ChangeUsername(
            int id,
            ChangeUsernameRequest changeUsernameRequest
        )
        {
            var userIdResult = this.User.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult.IsFailed)
            {
                return userIdResult.ToActionResult(this);
            }

            var userId = userIdResult.Value;
            if (userId != id)
            {
                return Result
                    .Fail(
                        new UnauthorizedError("Provided user id doesn't match authorized user's id")
                    )
                    .ToUnauthorizedActionResult(this);
            }

            var command = new ChangeUsernameCommand(
                userId,
                changeUsernameRequest.Username,
                changeUsernameRequest.Email
            );
            var result = await _mediator.Send(command);

            return result.ToActionResult(this);
        }

        [HttpPost("{id}/verify")]
        public async Task<IActionResult> VerifyEmail(int id, VerifyEmailRequest request)
        {
            var command = new VerifyEmailCommand(id, request.Token);
            var result = await _mediator.Send(command);

            return result.ToActionResult(this);
        }

        [HttpPost("recover")]
        public async Task<IActionResult> RecoverUser(RecoverUserRequest request)
        {
            var command = new RecoverUserCommand(request.Email);
            var result = await _mediator.Send(command);

            return result.ToActionResult(this);
        }

        [HttpPost("{id}/recover")]
        public async Task<IActionResult> VerifyRecoveryToken(
            int id,
            VerifyRecoveryTokenRequest request
        )
        {
            var command = new VerifyRecoveryTokenCommand(id, request.Token);
            var result = await _mediator.Send(command);

            return result.ToActionResult(this);
        }
    }
}
