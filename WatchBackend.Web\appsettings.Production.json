{"UseInMemoryDatabase": false, "Logging": {"LogLevel": {"Default": "Information", "System": "Information", "Microsoft": "Information", "Microsoft.AspNetCore": "Information"}}, "AllowedHosts": "watch.sap.re", "HashIdsOptions": {"Salt": "E3g*dDM=X-ZnXgq1HhPAEyc*JqFbaxhX2LfVjY?s2QKDTT2EidO5!JUkQ8Iia4KC"}, "JwtOptions": {"ValidIssuer": "https://watch.sap.re/", "ValidAudience": "https://watch.sap.re/", "Secret": "hon:-_Szn27SLd-9+w2pmXMrYtk8+B.0szj91Xq_.ja_*VWREH9-/Rm5nVl:n6.!", "ExpiresInMinutes": 5256000}, "ConnectionStrings": {"SqliteConnection": "Data Source=WatchBackendDatabase.db"}, "EmailOptions": {"MailtrapUsername": "api", "MailtrapPassword": "821f4db68d68fc4593c15d9985750b90", "MailtrapHost": "live.smtp.mailtrap.io", "MailtrapPort": 587, "FromAddress": "<EMAIL>", "FromName": "watch", "ConfirmationSubject": "Please confirm your email address", "ConfirmationBaseHref": "https://watch.sap.re/verify", "RecoverySubject": "User recovery request", "RecoveryBaseHref": "https://watch.sap.re/recover", "HeaderImgSrc": "https://watch.sap.re/watch2x.png", "WatchHref": "https://watch.sap.re/"}, "ConfirmationTokenProviderOptions": {"Name": "ConfirmationTokenProvider", "TokenLifespan": "00:10"}, "RecoveryTokenProviderOptions": {"Name": "RecoveryTokenProvider", "TokenLifespan": "00:10"}}