﻿using WatchBackend.Core.Entities;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Mapping
{
    public static class EntityToDomainMapper
    {
        public static Room ToDomain(this RoomEntity roomEntity)
        {
            return new Room
            {
                Id = roomEntity.Id,
                IdHash = roomEntity.IdHash,
                PlaybackState = (Models.PlaybackState)roomEntity.PlaybackState,
                PlaybackBaseTime = roomEntity.CurrentTime,
                PlaybackStartUtc = default,
                Playlist = roomEntity.Playlist.ToDomain(),
                Users = roomEntity.Users.Select(u => u.ToDomain()).ToList(),
            };
        }

        public static Playlist<SourceContainer> ToDomain(
            this SourceContainerPlaylistEntity playlistEntity
        )
        {
            return new Playlist<SourceContainer>
            {
                Id = playlistEntity.Id,
                CurrentIndex = playlistEntity.CurrentIndex,
                Items = playlistEntity.Items.Select(i => i.ToDomain()).ToList(),
            };
        }

        public static RoomUser ToDomain(this RoomUserEntity roomUserEntity)
        {
            return new RoomUser
            {
                Id = roomUserEntity.Id,
                UserId = roomUserEntity.UserId,
                Username = roomUserEntity.Username,
                Role = (RoomUserRoles)roomUserEntity.Role,
            };
        }

        public static SourceContainer ToDomain(this SourceContainerEntity sourceContainerEntity)
        {
            return new SourceContainer
            {
                Id = sourceContainerEntity.Id,
                Type = sourceContainerEntity.Type,
                Video = sourceContainerEntity.Video?.ToDomain(),
                Subtitles = sourceContainerEntity.Subtitles?.Select(s => s.ToDomain()).ToList(),
                Attributes = sourceContainerEntity.Attributes,
                ExternalInfo = sourceContainerEntity.ExternalInfo?.ToDomain(),
            };
        }

        public static VideoSource ToDomain(this VideoSourceEntity videoSourceEntity)
        {
            return new VideoSource
            {
                Id = videoSourceEntity.Id,
                Url = videoSourceEntity.Url,
                Attributes = videoSourceEntity.Attributes,
                Width = videoSourceEntity.Width,
                Height = videoSourceEntity.Height,
            };
        }

        public static SubtitleSource ToDomain(this SubtitleSourceEntity subtitleSourceEntity)
        {
            return new SubtitleSource
            {
                Id = subtitleSourceEntity.Id,
                Url = subtitleSourceEntity.Url,
                Label = subtitleSourceEntity.Label,
                SrcLang = subtitleSourceEntity.SrcLang,
                Offset = subtitleSourceEntity.Offset,
                Attributes = subtitleSourceEntity.Attributes,
            };
        }

        public static BaseExternalInfo ToDomain(this BaseExternalInfoEntity externalInfoEntity)
        {
            return externalInfoEntity switch
            {
                SeriesInfoEntity seriesInfoEntity => seriesInfoEntity.ToDomain(),
                MovieInfoEntity movieInfoEntity => movieInfoEntity.ToDomain(),
                _ => throw new NotSupportedException(
                    $"Unsupported external info entity type: {externalInfoEntity.GetType().Name}"
                ),
            };
        }

        public static SeriesInfo ToDomain(this SeriesInfoEntity seriesInfoEntity)
        {
            return new SeriesInfo
            {
                Id = seriesInfoEntity.Id,
                Title = seriesInfoEntity.Title ?? string.Empty,
                OriginalTitle = seriesInfoEntity.OriginalTitle ?? string.Empty,
                Description = seriesInfoEntity.Description ?? string.Empty,
                ReleaseDate = seriesInfoEntity.ReleaseDate ?? default,
                PosterImageUrl = seriesInfoEntity.PosterImageUrl ?? string.Empty,
                BackdropImageUrl = seriesInfoEntity.BackdropImageUrl ?? string.Empty,
                BackdropPlaceholderUrl = seriesInfoEntity.BackdropPlaceholderUrl ?? string.Empty,
                ImdbId = seriesInfoEntity.ImdbId ?? string.Empty,
                OriginCountries = seriesInfoEntity.OriginCountries ?? [],
                Genres = seriesInfoEntity.Genres ?? [],
                SpokenLanguages = seriesInfoEntity.SpokenLanguages ?? [],
                ProductionCompanies =
                    seriesInfoEntity.ProductionCompanies?.Select(c => c.ToDomain()) ?? [],
                Networks = seriesInfoEntity.Networks?.Select(c => c.ToDomain()) ?? [],
                SeasonNumber = seriesInfoEntity.SeasonNumber ?? 0,
                EpisodeNumber = seriesInfoEntity.EpisodeNumber ?? 0,
                EpisodeTitle = seriesInfoEntity.EpisodeTitle ?? string.Empty,
                Credits = seriesInfoEntity.Credits?.ToDomain() ?? SeriesCredits.Create(),
            };
        }

        public static MovieInfo ToDomain(this MovieInfoEntity movieInfoEntity)
        {
            return new MovieInfo
            {
                Id = movieInfoEntity.Id,
                Title = movieInfoEntity.Title ?? string.Empty,
                OriginalTitle = movieInfoEntity.OriginalTitle ?? string.Empty,
                Description = movieInfoEntity.Description ?? string.Empty,
                ReleaseDate = movieInfoEntity.ReleaseDate ?? default,
                PosterImageUrl = movieInfoEntity.PosterImageUrl ?? string.Empty,
                BackdropImageUrl = movieInfoEntity.BackdropImageUrl ?? string.Empty,
                BackdropPlaceholderUrl = movieInfoEntity.BackdropPlaceholderUrl ?? string.Empty,
                ImdbId = movieInfoEntity.ImdbId ?? string.Empty,
                OriginCountries = movieInfoEntity.OriginCountries ?? [],
                Genres = movieInfoEntity.Genres ?? [],
                SpokenLanguages = movieInfoEntity.SpokenLanguages ?? [],
                ProductionCompanies =
                    movieInfoEntity.ProductionCompanies?.Select(c => c.ToDomain()) ?? [],
                Networks = [],
                Tagline = movieInfoEntity.Tagline ?? string.Empty,
                Budget = movieInfoEntity.Budget ?? 0,
                Credits = movieInfoEntity.Credits?.ToDomain() ?? MovieCredits.Create(),
            };
        }

        public static SeriesCredits ToDomain(this SeriesCreditsEntity seriesCreditsEntity)
        {
            return new SeriesCredits
            {
                Id = seriesCreditsEntity.Id,
                Actors = seriesCreditsEntity.Actors?.Select(a => a.ToDomain()) ?? [],
                Writers = seriesCreditsEntity.Writers?.Select(w => w.ToDomain()) ?? [],
                Directors = seriesCreditsEntity.Directors?.Select(d => d.ToDomain()) ?? [],
                OriginalMusicComposers =
                    seriesCreditsEntity.OriginalMusicComposers?.Select(c => c.ToDomain()) ?? [],
                GuestActors = seriesCreditsEntity.GuestActors?.Select(a => a.ToDomain()) ?? [],
                Creators = seriesCreditsEntity.Creators?.Select(c => c.ToDomain()) ?? [],
            };
        }

        public static MovieCredits ToDomain(this MovieCreditsEntity movieCreditsEntity)
        {
            return new MovieCredits
            {
                Id = movieCreditsEntity.Id,
                Actors = movieCreditsEntity.Actors?.Select(a => a.ToDomain()) ?? [],
                Writers = movieCreditsEntity.Writers?.Select(w => w.ToDomain()) ?? [],
                Directors = movieCreditsEntity.Directors?.Select(d => d.ToDomain()) ?? [],
                OriginalMusicComposers =
                    movieCreditsEntity.OriginalMusicComposers?.Select(c => c.ToDomain()) ?? [],
            };
        }

        public static Actor ToDomain(this ActorEntity actorEntity)
        {
            return new Actor
            {
                Id = actorEntity.Id,
                Name = actorEntity.Name ?? string.Empty,
                ProfileImageUrl = actorEntity.ProfileImageUrl ?? string.Empty,
                Character = actorEntity.Character ?? string.Empty,
                Order = actorEntity.Order ?? 0,
            };
        }

        public static Crew ToDomain(this CrewEntity crewEntity)
        {
            return new Crew
            {
                Id = crewEntity.Id,
                Name = crewEntity.Name ?? string.Empty,
                ProfileImageUrl = crewEntity.ProfileImageUrl ?? string.Empty,
            };
        }

        public static Company ToDomain(this CompanyEntity companyEntity)
        {
            return new Company
            {
                Id = companyEntity.Id,
                Name = companyEntity.Name ?? string.Empty,
                LogoImageUrl = companyEntity.LogoImageUrl ?? string.Empty,
            };
        }
    }
}
