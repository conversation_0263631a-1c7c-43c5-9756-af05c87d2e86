using System.Collections.Concurrent;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WatchBackend.Core.Interfaces;

namespace WatchBackend.Infrastructure.Services
{
    public class DebounceService : IDebounceService, IDisposable
    {
        private readonly ILogger<DebounceService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly ConcurrentDictionary<string, DebounceEntry> _debounceEntries = new();
        private readonly TimeSpan _defaultDelay = TimeSpan.FromSeconds(2);
        private bool _disposed = false;

        public DebounceService(ILogger<DebounceService> logger, IServiceScopeFactory scopeFactory)
        {
            _logger = logger;
            _scopeFactory = scopeFactory;
        }

        public void DebounceAsync(string key, Func<Task> operation, TimeSpan delay)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(DebounceService));
            }

            if (_debounceEntries.TryGetValue(key, out var existingEntry))
            {
                existingEntry.Timer.Dispose();
            }

            var timer = new Timer(
                async _ =>
                {
                    try
                    {
                        _logger.LogDebug("Executing debounced operation for key: {Key}", key);
                        await operation();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error executing debounced operation for key: {Key}",
                            key
                        );
                    }
                    finally
                    {
                        _debounceEntries.TryRemove(key, out var _);
                    }
                },
                null,
                delay,
                Timeout.InfiniteTimeSpan
            );

            var newEntry = new DebounceEntry(operation, timer);

            _debounceEntries.AddOrUpdate(
                key,
                newEntry,
                (_, oldEntry) =>
                {
                    oldEntry.Timer.Dispose();
                    return newEntry;
                }
            );

            _logger.LogDebug(
                "Debounced operation scheduled for key: {Key} with delay: {Delay}ms",
                key,
                delay.TotalMilliseconds
            );
        }

        public void DebounceAsync(string key, Func<Task> operation)
        {
            DebounceAsync(key, operation, _defaultDelay);
        }

        public void DebounceAsync(
            string key,
            Func<IServiceProvider, Task> operation,
            TimeSpan delay
        )
        {
            DebounceAsync(
                key,
                () =>
                {
                    using var scope = _scopeFactory.CreateScope();
                    return operation(scope.ServiceProvider);
                },
                delay
            );
        }

        public void DebounceAsync(string key, Func<IServiceProvider, Task> operation)
        {
            DebounceAsync(key, operation, _defaultDelay);
        }

        public void DebounceAsync<TService>(
            string key,
            Func<TService, Task> operation,
            TimeSpan delay
        )
            where TService : notnull
        {
            DebounceAsync(
                key,
                sp =>
                {
                    var service = sp.GetRequiredService<TService>();
                    return operation(service);
                },
                delay
            );
        }

        public void DebounceAsync<TService>(string key, Func<TService, Task> operation)
            where TService : notnull
        {
            DebounceAsync(key, operation, _defaultDelay);
        }

        public async Task FlushAsync(string key)
        {
            if (_debounceEntries.TryRemove(key, out var entry))
            {
                entry.Timer.Dispose();

                try
                {
                    _logger.LogDebug("Flushing debounced operation for key: {Key}", key);
                    await entry.Operation();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error flushing debounced operation for key: {Key}", key);
                    throw;
                }
            }
        }

        public void Cancel(string key)
        {
            if (_debounceEntries.TryRemove(key, out var entry))
            {
                entry.Timer.Dispose();
                _logger.LogDebug("Cancelled debounced operation for key: {Key}", key);
            }
        }

        public void CancelAll()
        {
            var keys = _debounceEntries.Keys.ToList();
            foreach (var key in keys)
            {
                Cancel(key);
            }

            _logger.LogDebug("Cancelled all debounced operations");
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                CancelAll();
                _disposed = true;
            }
        }

        private record DebounceEntry(Func<Task> Operation, Timer Timer);
    }
}
