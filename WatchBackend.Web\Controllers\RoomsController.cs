﻿using System.Security.Claims;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WatchBackend.Core.Events.Commands;
using WatchBackend.Core.Events.Queries;
using WatchBackend.Core.Extensions;
using WatchBackend.Web.Extensions;

namespace WatchBackend.Web.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class RoomsController : ControllerBase
    {
        private readonly ILogger<RoomsController> _logger;
        private readonly IMediator _mediator;

        public RoomsController(ILogger<RoomsController> logger, IMediator mediator)
        {
            _logger = logger;
            _mediator = mediator;
        }

        [HttpGet("{idHash}")]
        public async Task<IActionResult> GetRoom(string idHash)
        {
            var query = new GetRoomQuery { RoomIdHash = idHash };
            var result = await _mediator.Send(query);

            return result.ToActionResult(this);
        }

        [HttpPost("")]
        [Authorize]
        public async Task<IActionResult> CreateRoom()
        {
            var userIdResult = this.User.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult.IsFailed)
            {
                return userIdResult.ToUnauthorizedActionResult(this);
            }

            var userId = userIdResult.Value;
            var command = new CreateRoomCommand(userId);
            var result = await _mediator.Send(command);
            if (result.IsFailed)
            {
                return result.ToActionResult(this);
            }

            var roomDto = result.Value;

            return CreatedAtAction("GetRoom", new { idHash = roomDto.IdHash }, roomDto);
        }

        [HttpPost("{idHash}/join")]
        [Authorize]
        public async Task<IActionResult> JoinRoom(string idHash)
        {
            var userIdResult = this.User.GetClaimAsInt(ClaimTypes.NameIdentifier);
            if (userIdResult.IsFailed)
            {
                return userIdResult.ToUnauthorizedActionResult(this);
            }

            var userId = userIdResult.Value;
            var command = new JoinRoomCommand(idHash, userId);
            var result = await _mediator.Send(command);

            return result.ToActionResult(this);
        }
    }
}
