﻿using WatchBackend.Core.Models;

namespace WatchBackend.Core.Dtos
{
    public class SourceContainerDto
    {
        public required int Id { get; set; }
        public required string Type { get; init; }
        public required VideoSourceDto? Video { get; init; }
        public required IEnumerable<SubtitleSourceDto>? Subtitles { get; init; }
        public required IDictionary<string, string>? Attributes { get; init; }
        public required BaseExternalInfoDto? ExternalInfo { get; init; }
    }

    public class VideoSourceDto
    {
        public required string Url { get; init; }
        public required IDictionary<string, string>? Attributes { get; init; }
        public required int? Width { get; init; }
        public required int? Height { get; init; }
    }

    public class SubtitleSourceDto
    {
        public required string Url { get; init; }
        public required string Label { get; init; }
        public required string SrcLang { get; init; }
        public required double Offset { get; set; }
        public required IDictionary<string, string>? Attributes { get; init; }
    }

    public class BaseExternalInfoDto
    {
        public required string Title { get; set; }
    }

    public class BaseMovieAndSeriesInfoDto : BaseExternalInfoDto
    {
        public required string OriginalTitle { get; set; }
        public required string Description { get; set; }
        public required DateOnly ReleaseDate { get; set; }
        public required string PosterImageUrl { get; set; }
        public required string BackdropImageUrl { get; set; }
        public required string BackdropPlaceholderUrl { get; set; }
        public required string ImdbId { get; set; }
        public required IEnumerable<string> OriginCountries { get; set; }
        public required IEnumerable<string> Genres { get; set; }
        public required IEnumerable<string> SpokenLanguages { get; set; }
        public required IEnumerable<CompanyDto> Networks { get; set; }
        public required IEnumerable<CompanyDto> ProductionCompanies { get; set; }
    }

    public class BaseMovieAndSeriesCreditsDto
    {
        public required IEnumerable<ActorDto> Actors { get; set; }
        public required IEnumerable<CrewDto> Writers { get; set; }
        public required IEnumerable<CrewDto> Directors { get; set; }
        public required IEnumerable<CrewDto> OriginalMusicComposers { get; set; }
    }

    public class SeriesInfoDto : BaseMovieAndSeriesInfoDto
    {
        public required int SeasonNumber { get; set; }
        public required int EpisodeNumber { get; set; }
        public required string EpisodeTitle { get; set; }
        public required SeriesCreditsDto Credits { get; set; }
    }

    public class SeriesCreditsDto : BaseMovieAndSeriesCreditsDto
    {
        public required IEnumerable<ActorDto> GuestActors { get; set; }
        public required IEnumerable<CrewDto> Creators { get; set; }

        public static SeriesCreditsDto Create()
        {
            return new SeriesCreditsDto
            {
                Actors = [],
                Writers = [],
                Directors = [],
                OriginalMusicComposers = [],
                GuestActors = [],
                Creators = [],
            };
        }
    }

    public class MovieInfoDto : BaseMovieAndSeriesInfoDto
    {
        public required string Tagline { get; set; }
        public required uint Budget { get; set; }
        public required MovieCreditsDto Credits { get; set; }
    }

    public class MovieCreditsDto : BaseMovieAndSeriesCreditsDto
    {
        public static MovieCreditsDto Create()
        {
            return new MovieCreditsDto
            {
                Actors = [],
                Writers = [],
                Directors = [],
                OriginalMusicComposers = [],
            };
        }
    }

    public class CompanyDto
    {
        public required string Name { get; set; }
        public required string LogoImageUrl { get; set; }
    }

    public class CrewDto
    {
        public required string Name { get; set; }
        public required string ProfileImageUrl { get; set; }
    }

    public class ActorDto : CrewDto
    {
        public required string Character { get; set; }
        public required int Order { get; set; }
    }
}
