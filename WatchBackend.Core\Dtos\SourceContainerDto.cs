﻿using WatchBackend.Core.Models;

namespace WatchBackend.Core.Dtos
{
    public class SourceContainerDto
    {
        public int Id { get; set; }
        public string Type { get; init; } = string.Empty;
        public VideoSourceDto? Video { get; init; }
        public IEnumerable<SubtitleSourceDto>? Subtitles { get; init; }
        public IDictionary<string, string>? Attributes { get; init; }
        public BaseExternalInfoDto? ExternalInfo { get; init; }
    }

    public class VideoSourceDto
    {
        public string Url { get; init; } = string.Empty;
        public IDictionary<string, string>? Attributes { get; init; }
        public int? Width { get; init; }
        public int? Height { get; init; }
    }

    public class SubtitleSourceDto
    {
        public string Url { get; init; } = string.Empty;
        public string Label { get; init; } = string.Empty;
        public string SrcLang { get; init; } = string.Empty;
        public double Offset { get; set; } = 0;
        public IDictionary<string, string>? Attributes { get; init; }
    }

    public class BaseExternalInfoDto
    {
        public string Title { get; set; } = string.Empty;
    }

    public class BaseMovieAndSeriesInfoDto : BaseExternalInfoDto
    {
        public string OriginalTitle { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateOnly ReleaseDate { get; set; }
        public string PosterImageUrl { get; set; } = string.Empty;
        public string BackdropImageUrl { get; set; } = string.Empty;
        public string BackdropPlaceholderUrl { get; set; } = string.Empty;
        public string ImdbId { get; set; } = string.Empty;
        public IEnumerable<string> OriginCountries { get; set; } = [];
        public IEnumerable<string> Genres { get; set; } = [];
        public IEnumerable<string> SpokenLanguages { get; set; } = [];
        public IEnumerable<CompanyDto> Networks { get; set; } = [];
        public IEnumerable<CompanyDto> ProductionCompanies { get; set; } = [];
    }

    public class BaseMovieAndSeriesCreditsDto
    {
        public IEnumerable<ActorDto> Actors { get; set; } = [];
        public IEnumerable<CrewDto> Writers { get; set; } = [];
        public IEnumerable<CrewDto> Directors { get; set; } = [];
        public IEnumerable<CrewDto> OriginalMusicComposers { get; set; } = [];
    }

    public class SeriesInfoDto : BaseMovieAndSeriesInfoDto
    {
        public int SeasonNumber { get; set; }
        public int EpisodeNumber { get; set; }
        public string EpisodeTitle { get; set; } = string.Empty;
        public SeriesCreditsDto Credits { get; set; } = new SeriesCreditsDto();
    }

    public class SeriesCreditsDto : BaseMovieAndSeriesCreditsDto
    {
        public IEnumerable<ActorDto> GuestActors { get; set; } = [];
        public IEnumerable<CrewDto> Creators { get; set; } = [];
    }

    public class MovieInfoDto : BaseMovieAndSeriesInfoDto
    {
        public string Tagline { get; set; } = string.Empty;
        public uint Budget { get; set; }
        public MovieCreditsDto Credits { get; set; } = new MovieCreditsDto();
    }

    public class MovieCreditsDto : BaseMovieAndSeriesCreditsDto { }

    public class CompanyDto
    {
        public string Name { get; set; } = string.Empty;
        public string LogoImageUrl { get; set; } = string.Empty;
    }

    public class CrewDto
    {
        public string Name { get; set; } = string.Empty;
        public string ProfileImageUrl { get; set; } = string.Empty;
    }

    public class ActorDto : CrewDto
    {
        public string Character { get; set; } = string.Empty;
        public int Order { get; set; }
    }
}
