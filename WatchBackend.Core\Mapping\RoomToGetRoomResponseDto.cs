﻿using Mapster;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Models;

namespace WatchBackend.Core.Mapping
{
    public class RoomToGetRoomResponseDto : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config
                .ForType<Room, GetRoomResponseDto>()
                .Map(dst => dst.IdHash, src => src.IdHash)
                .Map(dst => dst.CurrentSourceContainer, src => src.Playlist.CurrentItem);
        }
    }
}
