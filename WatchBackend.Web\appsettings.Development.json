{"UseInMemoryDatabase": false, "ConnectionStrings": {"SqliteConnection": "Data Source=WatchBackendDatabaseDEV.db"}, "Logging": {"LogLevel": {"Default": "Debug", "System": "Debug", "Microsoft": "Debug", "Microsoft.AspNetCore": "Debug"}}, "HashIdsOptions": {"Salt": "Bkm)iz9DZy2n=#E/h(K=?_95zl*t#B@q?FiM?sXmokkSDV9yGvG3/Xw*6k2?zgL_"}, "JwtOptions": {"ValidIssuer": "https://localhost:7271/", "ValidAudience": "https://localhost:7271/", "Secret": "ulv.r:la0BQzrwz+D5rqFnv5JLeLJGxzdK/RMgX*Ziog!4DGuGc-_dN_P1Ke5Pmn", "ExpiresInMinutes": 5256000}, "EmailOptions": {"MailtrapUsername": "api", "MailtrapPassword": "821f4db68d68fc4593c15d9985750b90", "MailtrapHost": "live.smtp.mailtrap.io", "MailtrapPort": 587, "FromAddress": "<EMAIL>", "FromName": "watch dev", "ConfirmationSubject": "Please confirm your email address", "ConfirmationBaseHref": "http://localhost:5173/verify", "RecoverySubject": "User recovery request", "RecoveryBaseHref": "http://localhost:5173/recover", "HeaderImgSrc": "http://localhost:5173/watch2x.png", "WatchHref": "http://localhost:5173/"}, "ConfirmationTokenProviderOptions": {"Name": "ConfirmationTokenProvider", "TokenLifespan": "00:10"}, "RecoveryTokenProviderOptions": {"Name": "RecoveryTokenProvider", "TokenLifespan": "00:10"}}