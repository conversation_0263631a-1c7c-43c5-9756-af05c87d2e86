﻿using WatchBackend.Core.Entities;
using WatchBackend.Core.Queries;

namespace WatchBackend.Core.Interfaces
{
    public interface IPlaylistRepository
    {
        Task<SourceContainerPlaylistEntity> GetByIdAsync(
            int id,
            PlaylistQuery? query = null,
            bool noTracking = false
        );
        Task<SourceContainerPlaylistEntity?> GetByIdOrDefaultAsync(
            int id,
            PlaylistQuery? query = null,
            bool noTracking = false
        );
    }
}
