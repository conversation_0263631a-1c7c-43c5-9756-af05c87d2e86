﻿using FluentResults;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using WatchBackend.Core.Dtos;
using WatchBackend.Core.Errors;

namespace WatchBackend.Web.Extensions
{
    public static class ResultExtensions
    {
        public static ActionResult ToActionResult(this Result result, ControllerBase controller)
        {
            if (result.IsSuccess)
            {
                return controller.Ok();
            }

            return GetFailedActionResult(result, controller);
        }

        public static ActionResult ToActionResult<TValue>(
            this Result<TValue> result,
            ControllerBase controller
        )
        {
            if (result.IsSuccess)
            {
                return controller.Ok(result.Value);
            }

            return GetFailedActionResult(result, controller);
        }

        public static ActionResult ToUnauthorizedActionResult(
            this Result result,
            ControllerBase controller
        )
        {
            var errorsDto = result.Errors.Adapt<List<FailedResultErrorDto>>();
            var failedResultDto = new FailedResultDto { Errors = errorsDto };

            return controller.Unauthorized(failedResultDto);
        }

        public static ActionResult ToUnauthorizedActionResult<TValue>(
            this Result<TValue> result,
            ControllerBase controller
        )
        {
            var errorsDto = result.Errors.Adapt<List<FailedResultErrorDto>>();
            var failedResultDto = new FailedResultDto { Errors = errorsDto };

            return controller.Unauthorized(failedResultDto);
        }

        public static ResultDto ToResultDto(this Result result)
        {
            if (result.IsSuccess)
            {
                return new ResultDto { IsSuccess = true };
            }

            return GetFailedResultDto(result);
        }

        public static ResultDto<TValue> ToResultDto<TValue>(this Result<TValue> result)
        {
            if (result.IsSuccess)
            {
                return new ResultDto<TValue> { IsSuccess = true, Value = result.Value };
            }

            return GetFailedResultDto(result);
        }

        private static ActionResult GetFailedActionResult(
            ResultBase result,
            ControllerBase controller
        )
        {
            var errorsDto = result.Errors.Adapt<List<FailedResultErrorDto>>();
            var failedResultDto = new FailedResultDto { Errors = errorsDto };

            if (result.HasError<ExceptionalError>())
            {
                return controller.StatusCode(
                    StatusCodes.Status500InternalServerError,
                    failedResultDto
                );
            }

            if (result.HasError<NotFoundError>())
            {
                return controller.NotFound(failedResultDto);
            }

            if (result.HasError<InvalidError>())
            {
                return controller.UnprocessableEntity(failedResultDto);
            }

            if (result.HasError<UnauthorizedError>())
            {
                return controller.Unauthorized(failedResultDto);
            }

            return controller.BadRequest(failedResultDto);
        }

        private static ResultDto GetFailedResultDto(Result result)
        {
            var errorDto = GetErrorDto(result);

            return new ResultDto { IsSuccess = false, Error = errorDto };
        }

        private static ResultDto<TValue> GetFailedResultDto<TValue>(Result<TValue> result)
        {
            var errorDto = GetErrorDto(result);

            return new ResultDto<TValue> { IsSuccess = false, Error = errorDto };
        }

        private static ErrorDto GetErrorDto(ResultBase result)
        {
            var firstError = result.Errors[0];
            var type = firstError switch
            {
                ExceptionalError => ErrorDtoType.Exceptional,
                InvalidError => ErrorDtoType.Invalid,
                NotFoundError => ErrorDtoType.NotFound,
                UnauthorizedError => ErrorDtoType.Unauthorized,
                null => ErrorDtoType.Default,
                _ => ErrorDtoType.Default,
            };

            return new ErrorDto { Type = type, Message = firstError?.Message ?? String.Empty };
        }
    }
}
