﻿using Mapster;
using Microsoft.Extensions.DependencyInjection;

namespace WatchBackend.Core
{
    public static class ConfigureServices
    {
        public static IServiceCollection AddCoreServices(this IServiceCollection services)
        {
            services.AddMediatR(cfg =>
                cfg.RegisterServicesFromAssembly(typeof(ConfigureServices).Assembly)
            );

            TypeAdapterConfig.GlobalSettings.Scan(typeof(ConfigureServices).Assembly);

            return services;
        }
    }
}
