﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;

namespace WatchBackend.Core.Events.Commands
{
    public class VerifyRecoveryTokenCommand : IRequest<Result<VerifyRecoveryTokenResponseDto>>
    {
        public int Id { get; set; }
        public string Token { get; set; }

        public VerifyRecoveryTokenCommand(int id, string token)
        {
            Id = id;
            Token = token;
        }
    }
}
