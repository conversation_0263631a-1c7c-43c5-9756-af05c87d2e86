﻿using FluentEmail.Core.Defaults;
using FluentEmail.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace WatchBackend.Infrastructure.Extensions
{
    internal static class FluidEmailFluidBuilderExtensions
    {
        internal static FluentEmailServicesBuilder AddPassthroughRenderer(
            this FluentEmailServicesBuilder builder
        )
        {
            builder.Services.TryAddSingleton<ITemplateRenderer, ReplaceRenderer>();

            return builder;
        }
    }
}
