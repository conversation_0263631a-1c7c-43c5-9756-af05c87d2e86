﻿using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WatchBackend.Core.Entities;

namespace WatchBackend.Data.Configurations
{
    public class SourceContainerConfiguration : IEntityTypeConfiguration<SourceContainerEntity>
    {
        public void Configure(EntityTypeBuilder<SourceContainerEntity> builder)
        {
            var options = new JsonSerializerOptions(JsonSerializerDefaults.General);

            builder
                .Property(c => c.Attributes)
                .HasConversion(
                    a => JsonSerializer.Serialize(a, options),
                    s => JsonSerializer.Deserialize<IDictionary<string, string>>(s, options),
                    ValueComparer.CreateDefault(typeof(IDictionary<string, string>), true)
                );
        }
    }
}
