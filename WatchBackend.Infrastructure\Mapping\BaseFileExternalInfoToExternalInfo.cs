﻿using Mapster;
using WatchBackend.Core.Models;

namespace WatchBackend.Infrastructure.Mapping
{
    public class BaseFileExternalInfoToExternalInfo : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config
                .ForType<BaseFileExternalInfo, BaseExternalInfo>()
                .Include<FileMovieInfo, MovieInfo>()
                .Include<FileSeriesInfo, SeriesInfo>();
        }
    }
}
