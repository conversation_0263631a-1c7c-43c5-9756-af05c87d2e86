﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;

namespace WatchBackend.Core.Events.Commands
{
    public class VerifyEmailCommand : IRequest<Result<VerifyEmailResponseDto>>
    {
        public int Id { get; set; }
        public string Token { get; set; }

        public VerifyEmailCommand(int id, string token)
        {
            Id = id;
            Token = token;
        }
    }
}
