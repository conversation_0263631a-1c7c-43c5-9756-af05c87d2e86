﻿using WatchBackend.Core.Models;

namespace WatchBackend.Web.Mapping
{
    public class ConnectionToRoomsMapping
    {
        private readonly Dictionary<string, List<Room>> _connectionToRooms = new();

        public void Add(string connectionId, Room room)
        {
            lock (_connectionToRooms)
            {
                if (!_connectionToRooms.TryGetValue(connectionId, out List<Room>? rooms))
                {
                    rooms = new List<Room>();
                    _connectionToRooms.Add(connectionId, rooms);
                }

                lock (rooms)
                {
                    rooms.Add(room);
                }
            }
        }

        public void Remove(string connectionId)
        {
            lock (_connectionToRooms)
            {
                _connectionToRooms.Remove(connectionId);
            }
        }

        public IReadOnlyList<Room> GetRooms(string connectionId)
        {
            if (_connectionToRooms.TryGetValue(connectionId, out List<Room>? rooms))
            {
                return rooms;
            }

            return new List<Room>();
        }
    }
}
