﻿using FluentResults;
using WatchBackend.Core.Dtos;

namespace WatchBackend.Core.Mapping
{
    public static class ResultErrorToDtoMapper
    {
        public static FailedResultErrorDto ToDto(this IError error)
        {
            return new FailedResultErrorDto { Message = error.Message };
        }

        public static IEnumerable<FailedResultErrorDto> ToDtos(this IEnumerable<IError> errors)
        {
            return errors.Select(e => e.ToDto());
        }
    }
}
