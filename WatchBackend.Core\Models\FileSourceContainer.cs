﻿namespace WatchBackend.Core.Models
{
    public class FileSourceContainer
    {
        public string Type { get; init; } = String.Empty;
        public FileVideoSource Video { get; init; } = new FileVideoSource();
        public IEnumerable<FileSubtitleSource> Subtitles { get; init; } = [];
        public BaseFileExternalInfo ExternalInfo { get; init; } = new BaseFileExternalInfo();
    }

    public class FileVideoSource
    {
        public string Filename { get; init; } = string.Empty;
        public string Type { get; init; } = string.Empty;
        public int? Width { get; init; }
        public int? Height { get; init; }
    }

    public class FileSubtitleSource
    {
        public string Label { get; init; } = string.Empty;
        public string SrcLang { get; init; } = string.Empty;
        public string Filename { get; init; } = string.Empty;
    }

    public class BaseFileExternalInfo
    {
        public string Title { get; set; } = string.Empty;
    }

    public class BaseFileMovieAndSeriesInfo : BaseFileExternalInfo
    {
        public string OriginalTitle { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateOnly ReleaseDate { get; set; }
        public string PosterImageUrl { get; set; } = string.Empty;
        public string BackdropImageUrl { get; set; } = string.Empty;
        public string BackdropPlaceholderUrl { get; set; } = string.Empty;
        public string ImdbId { get; set; } = string.Empty;
        public IEnumerable<string> OriginCountries { get; set; } = [];
        public IEnumerable<string> Genres { get; set; } = [];
        public IEnumerable<string> SpokenLanguages { get; set; } = [];
        public IEnumerable<FileCompany> Networks { get; set; } = [];
        public IEnumerable<FileCompany> ProductionCompanies { get; set; } = [];
    }

    public class BaseFileMovieAndSeriesCredits
    {
        public IEnumerable<FileActor> Actors { get; set; } = [];
        public IEnumerable<FileCrew> Writers { get; set; } = [];
        public IEnumerable<FileCrew> Directors { get; set; } = [];
        public IEnumerable<FileCrew> OriginalMusicComposers { get; set; } = [];
    }

    public class FileSeriesInfo : BaseFileMovieAndSeriesInfo
    {
        public int SeasonNumber { get; set; }
        public int EpisodeNumber { get; set; }
        public string EpisodeTitle { get; set; } = string.Empty;
        public FileSeriesCredits Credits { get; set; } = new FileSeriesCredits();
    }

    public class FileSeriesCredits : BaseFileMovieAndSeriesCredits
    {
        public IEnumerable<FileActor> GuestActors { get; set; } = [];
        public IEnumerable<FileCrew> Creators { get; set; } = [];
    }

    public class FileMovieInfo : BaseFileMovieAndSeriesInfo
    {
        public string Tagline { get; set; } = string.Empty;
        public uint Budget { get; set; }
        public FileMovieCredits Credits { get; set; } = new FileMovieCredits();
    }

    public class FileMovieCredits : BaseFileMovieAndSeriesCredits { }

    public class FileCompany
    {
        public string Name { get; set; } = string.Empty;
        public string LogoImageUrl { get; set; } = string.Empty;
    }

    public class FileCrew
    {
        public string Name { get; set; } = string.Empty;
        public string ProfileImageUrl { get; set; } = string.Empty;
    }

    public class FileActor : FileCrew
    {
        public string Character { get; set; } = string.Empty;
        public int Order { get; set; }
    }
}
