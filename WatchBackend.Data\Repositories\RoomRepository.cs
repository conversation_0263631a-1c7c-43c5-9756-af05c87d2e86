﻿using Microsoft.EntityFrameworkCore;
using WatchBackend.Core.Entities;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Queries;

namespace WatchBackend.Data.Repositories
{
    internal class RoomRepository : IRoomRepository
    {
        private readonly WatchBackendDbContext _context;

        public RoomRepository(WatchBackendDbContext context)
        {
            _context = context;
        }

        public void Add(RoomEntity roomEntity)
        {
            _context.Rooms.Add(roomEntity);
        }

        public void Update(RoomEntity roomEntity)
        {
            _context.Rooms.Update(roomEntity);
        }

        public Task<RoomEntity?> GetByIdAsync(
            int id,
            RoomQuery? roomQuery = null,
            bool noTracking = false
        )
        {
            var rooms = _context.Rooms.AsQueryable();
            if (roomQuery is not null)
            {
                rooms = rooms.ApplyIncludes(roomQuery);
            }

            if (noTracking)
            {
                rooms = rooms.AsNoTrackingWithIdentityResolution();
            }

            rooms = rooms.AsSplitQuery();

            return rooms.FirstOrDefaultAsync(r => r.Id == id);
        }
    }

    internal static class QueryableRoomEntityExtensions
    {
        public static IQueryable<RoomEntity> ApplyIncludes(
            this IQueryable<RoomEntity> rooms,
            RoomQuery roomQuery
        )
        {
            if (roomQuery.IncludeRoomUsers)
            {
                rooms = rooms.Include(r => r.Users);
            }

            if (roomQuery.IncludeRoomPlaylist)
            {
                rooms = rooms
                    .Include(r => r.Playlist)
                    .ThenInclude(p => p.Items)
                    .ThenInclude(i => i.Video)
                    .Include(r => r.Playlist)
                    .ThenInclude(p => p.Items)
                    .ThenInclude(i => i.Subtitles);
            }

            return rooms;
        }
    }
}
