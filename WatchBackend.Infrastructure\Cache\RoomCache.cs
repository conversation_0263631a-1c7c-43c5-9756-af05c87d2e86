﻿using System.Collections.Concurrent;
using WatchBackend.Core.Models;
using WatchBackend.Infrastructure.Interfaces;

namespace WatchBackend.Infrastructure.Cache
{
    public class RoomCache : IRoomCache
    {
        private readonly ConcurrentDictionary<int, Room> _roomIdToRoomMap = [];

        public ICollection<int> Keys => _roomIdToRoomMap.Keys;

        public ICollection<Room> Values => _roomIdToRoomMap.Values;

        public bool TryAdd(int roomId, Room room)
        {
            return _roomIdToRoomMap.TryAdd(roomId, room);
        }

        public bool TryGetValue(int roomId, out Room room)
        {
            return _roomIdToRoomMap.TryGetValue(roomId, out room);
        }
    }
}
