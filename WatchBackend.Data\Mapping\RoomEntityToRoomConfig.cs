﻿using Mapster;
using WatchBackend.Core.Entities;
using WatchBackend.Core.Models;

namespace WatchBackend.Data.Mapping
{
    public class RoomEntityToRoomConfig : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config
                .NewConfig<RoomEntity, Room>()
                .Map(dst => dst.PlaybackBaseTime, src => src.CurrentTime)
                .Ignore(dst => dst.CurrentTime)
                .AfterMapping((src, dst) => this.AddRoomEntityUsersToRoom(src, dst))
                .AfterMapping((src, dst) => this.MapPlaylist(src, dst));
        }

        private Room AddRoomEntityUsersToRoom(RoomEntity roomUserEntity, Room room)
        {
            roomUserEntity.Users.ForEach(userEntity =>
            {
                var roomUser = userEntity.Adapt<RoomUser>();
                room.AddUser(roomUser);
            });

            return room;
        }

        private Room MapPlaylist(RoomEntity roomEntity, Room room)
        {
            //room.Playlist.Id = roomEntity.Playlist.Id;
            roomEntity.Playlist.Adapt(room.Playlist);

            //foreach (var item in roomEntity.Playlist.Items)
            //{
            //    room.Playlist.Add(item.Adapt<SourceContainer>());
            //}

            room.Playlist.SetCurrentIndex(roomEntity.Playlist.CurrentIndex);

            return room;
        }
    }
}
