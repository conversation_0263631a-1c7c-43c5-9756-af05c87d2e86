﻿using Mapster;
using Microsoft.AspNetCore.SignalR;
using Microsoft.OpenApi.Models;
using WatchBackend.Core.Interfaces;
using WatchBackend.Web.Providers;
using WatchBackend.Web.Resolvers;
using WatchBackend.Web.Services;

namespace WatchBackend.Web
{
    public static class ConfigureServices
    {
        public static IServiceCollection AddWebServices(this IServiceCollection services)
        {
            services.AddSingleton<IUserIdProvider, NameIdentifierUserIdProvider>();
            services.AddSingleton<IRoomHubService, RoomHubService>();

            services.Configure<RouteOptions>(options => options.LowercaseUrls = true);

            // Add services to the container.
            services
                .AddControllers()
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.TypeInfoResolver = new PolymorphicResolver();
                });
            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(setupAction =>
            {
                setupAction.AddSecurityDefinition(
                    "Bearer",
                    new OpenApiSecurityScheme
                    {
                        Type = SecuritySchemeType.Http,
                        Scheme = "Bearer",
                        BearerFormat = "JWT",
                        In = ParameterLocation.Header,
                        Description = "JWT Authorization header using the Bearer scheme",
                    }
                );

                setupAction.AddSecurityRequirement(
                    new OpenApiSecurityRequirement
                    {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer",
                                },
                            },
                            Array.Empty<string>()
                        },
                    }
                );
            });

            services
                .AddSignalR()
                .AddJsonProtocol(options =>
                {
                    options.PayloadSerializerOptions.TypeInfoResolver = new PolymorphicResolver();
                });

            TypeAdapterConfig.GlobalSettings.Scan(typeof(ConfigureServices).Assembly);

            return services;
        }
    }
}
