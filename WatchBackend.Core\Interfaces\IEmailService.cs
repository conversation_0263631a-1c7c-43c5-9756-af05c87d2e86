﻿using FluentResults;

namespace WatchBackend.Core.Interfaces
{
    public interface IEmailService
    {
        Task<Result> SendConfirmationEmail(
            int id,
            string userEmailAddress,
            string confirmationToken,
            string desiredUsername
        );
        Task<Result> SendRecoveryEmail(
            int id,
            string username,
            string emailAddress,
            string recoveryToken
        );
    }
}
