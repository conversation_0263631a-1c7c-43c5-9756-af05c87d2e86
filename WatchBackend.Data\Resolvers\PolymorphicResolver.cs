﻿using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using WatchBackend.Core.Entities;

namespace WatchBackend.Data.Resolvers
{
    public class PolymorphicResolver : DefaultJsonTypeInfoResolver
    {
        public override JsonTypeInfo GetTypeInfo(Type type, JsonSerializerOptions options)
        {
            var jsonTypeInfo = base.GetTypeInfo(type, options);

            Type baseExternalInfoDtoType = typeof(BaseExternalInfoEntity);
            if (jsonTypeInfo.Type == baseExternalInfoDtoType)
            {
                jsonTypeInfo.PolymorphismOptions = new JsonPolymorphismOptions
                {
                    TypeDiscriminatorPropertyName = "$external_info_type",
                    IgnoreUnrecognizedTypeDiscriminators = true,
                    UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FailSerialization,
                    DerivedTypes =
                    {
                        new JsonDerivedType(typeof(MovieInfoEntity), "movie"),
                        new JsonDerivedType(typeof(SeriesInfoEntity), "series"),
                    },
                };
            }

            return jsonTypeInfo;
        }
    }
}
