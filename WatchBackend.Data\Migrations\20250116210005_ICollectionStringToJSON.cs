﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WatchBackend.Data.Migrations
{
    /// <inheritdoc />
    public partial class ICollectionStringToJSON : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CompanyEntity_BaseExternalInfoEntity_MovieInfoEntityId",
                table: "CompanyEntity");

            migrationBuilder.DropForeignKey(
                name: "FK_CompanyEntity_BaseExternalInfoEntity_SeriesInfoEntityId1",
                table: "CompanyEntity");

            migrationBuilder.DropIndex(
                name: "IX_CompanyEntity_MovieInfoEntityId",
                table: "CompanyEntity");

            migrationBuilder.DropColumn(
                name: "MovieInfoEntityId",
                table: "CompanyEntity");

            migrationBuilder.RenameColumn(
                name: "SeriesInfoEntityId1",
                table: "CompanyEntity",
                newName: "BaseMovieAndSeriesInfoEntityId");

            migrationBuilder.RenameIndex(
                name: "IX_CompanyEntity_SeriesInfoEntityId1",
                table: "CompanyEntity",
                newName: "IX_CompanyEntity_BaseMovieAndSeriesInfoEntityId");

            migrationBuilder.AddForeignKey(
                name: "FK_CompanyEntity_BaseExternalInfoEntity_BaseMovieAndSeriesInfoEntityId",
                table: "CompanyEntity",
                column: "BaseMovieAndSeriesInfoEntityId",
                principalTable: "BaseExternalInfoEntity",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CompanyEntity_BaseExternalInfoEntity_BaseMovieAndSeriesInfoEntityId",
                table: "CompanyEntity");

            migrationBuilder.RenameColumn(
                name: "BaseMovieAndSeriesInfoEntityId",
                table: "CompanyEntity",
                newName: "SeriesInfoEntityId1");

            migrationBuilder.RenameIndex(
                name: "IX_CompanyEntity_BaseMovieAndSeriesInfoEntityId",
                table: "CompanyEntity",
                newName: "IX_CompanyEntity_SeriesInfoEntityId1");

            migrationBuilder.AddColumn<int>(
                name: "MovieInfoEntityId",
                table: "CompanyEntity",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_CompanyEntity_MovieInfoEntityId",
                table: "CompanyEntity",
                column: "MovieInfoEntityId");

            migrationBuilder.AddForeignKey(
                name: "FK_CompanyEntity_BaseExternalInfoEntity_MovieInfoEntityId",
                table: "CompanyEntity",
                column: "MovieInfoEntityId",
                principalTable: "BaseExternalInfoEntity",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CompanyEntity_BaseExternalInfoEntity_SeriesInfoEntityId1",
                table: "CompanyEntity",
                column: "SeriesInfoEntityId1",
                principalTable: "BaseExternalInfoEntity",
                principalColumn: "Id");
        }
    }
}
