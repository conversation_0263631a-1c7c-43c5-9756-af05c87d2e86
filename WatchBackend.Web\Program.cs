using Microsoft.EntityFrameworkCore;
using WatchBackend.Core;
using WatchBackend.Data;
using WatchBackend.Infrastructure;
using WatchBackend.Web.Hubs;

namespace WatchBackend.Web
{
    public static class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            builder.Logging.ClearProviders();
            builder.Logging.AddConsole();

            builder.Services.AddCors();
            builder.Services.AddCoreServices();
            builder.Services.AddDataServices(builder.Configuration);
            builder.Services.AddInfrastructureServices(builder.Configuration);
            builder.Services.AddWebServices();
            builder.Services.AddHttpLogging(_ => { });

            var app = builder.Build();

            app.UseHttpLogging();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI();
            }

            if (app.Environment.IsDevelopment())
            {
                app.UseCors(b =>
                    b.AllowAnyMethod()
                        .AllowAnyHeader()
                        .SetIsOriginAllowed(origin => true)
                        .AllowCredentials()
                );
            }
            else
            {
                app.UseHttpsRedirection();
            }

            app.UseAuthentication();
            app.UseAuthorization();

            app.MapControllers();

            app.MapHub<RoomHub>("/roomHub");

#if DEBUG
            app.EnsureMigrationOf<WatchBackendDbContext>();
#else
            app.EnsureMigrationOf<WatchBackendDbContext>();
#endif

            app.Run();
        }

        private static void EnsureMigrationOf<T>(this IApplicationBuilder app)
            where T : DbContext
        {
            using (var scope = app.ApplicationServices.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<T>();
                if (context == null)
                {
                    Console.WriteLine("Migration target is null");
                    return;
                }

                context.Database.Migrate();
            }
        }
    }
}
