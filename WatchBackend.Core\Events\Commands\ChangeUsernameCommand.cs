﻿using FluentResults;
using MediatR;
using WatchBackend.Core.Dtos;

namespace WatchBackend.Core.Events.Commands
{
    public class ChangeUsernameCommand : IRequest<Result<ChangeUsernameResponseDto>>
    {
        public int UserId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;

        public ChangeUsernameCommand(int userId, string username, string email)
        {
            UserId = userId;
            Username = username;
            Email = email;
        }
    }
}
