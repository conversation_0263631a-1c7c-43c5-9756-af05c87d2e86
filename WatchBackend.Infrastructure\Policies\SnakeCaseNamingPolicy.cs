﻿using System.Text;
using System.Text.Json;

namespace WatchBackend.Infrastructure.Policies
{
    public class SnakeCaseNamingPolicy : JsonNamingPolicy
    {
        public override string ConvertName(string name)
        {
            // Conversion logic to snake_case
            if (string.IsNullOrEmpty(name))
            {
                return name;
            }

            StringBuilder builder = new StringBuilder();
            builder.Append(char.ToLowerInvariant(name[0]));

            for (int i = 1; i < name.Length; i++)
            {
                char c = name[i];
                if (char.IsUpper(c))
                {
                    builder.Append('_');
                    builder.Append(char.ToLowerInvariant(c));
                }
                else
                {
                    builder.Append(c);
                }
            }

            return builder.ToString();
        }
    }
}
