﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using FluentResults;
using Mapster;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Models;
using WatchBackend.Data.Models;
using WatchBackend.Infrastructure.Extensions;
using WatchBackend.Infrastructure.Models;

namespace WatchBackend.Infrastructure.Services
{
    internal class UserService : IUserService
    {
        private const string CONFIRMATION_TOKEN_PURPOSE = "confirm-email";
        private const string RECOVERY_TOKEN_PURPOSE = "confirm-email";

        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IUsernameService _usernameService;
        private readonly JwtOptions _jwtOptions;
        private readonly ConfirmationTokenProviderOptions _confirmationTokenProviderOptions;
        private readonly RecoveryTokenProviderOptions _recoveryTokenProviderOptions;

        public UserService(
            UserManager<ApplicationUser> userManager,
            IUsernameService usernameService,
            IOptions<JwtOptions> jwtOptions,
            IOptions<ConfirmationTokenProviderOptions> confirmationTokenProviderOptions,
            IOptions<RecoveryTokenProviderOptions> recoveryTokenProviderOptions
        )
        {
            _userManager = userManager;
            _usernameService = usernameService;
            _jwtOptions = jwtOptions.Value;
            _confirmationTokenProviderOptions = confirmationTokenProviderOptions.Value;
            _recoveryTokenProviderOptions = recoveryTokenProviderOptions.Value;
        }

        public async Task<Result<User>> CreateUser()
        {
            var username = await GetUniqueUsername();
            var identityUser = new ApplicationUser { UserName = username };

            var identityResult = await _userManager.CreateAsync(identityUser);
            var user = identityUser.Adapt<User>();

            return identityResult.MapToUserResult(user);
        }

        public async Task<Result<User>> GetUserById(int id)
        {
            var identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<User>();
            }

            var identityUser = identityUserResult.Value;
            var user = identityUser.Adapt<User>();

            return Result.Ok(user);
        }

        public async Task<Result<User>> GetUserByEmail(string email)
        {
            var identityUserResult = await GetIdentityUserByEmail(email);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<User>();
            }

            var identityUser = identityUserResult.Value;
            var user = identityUser.Adapt<User>();

            return Result.Ok(user);
        }

        public async Task<Result<User>> RerollUsername(int id)
        {
            var identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<User>();
            }

            var identityUser = identityUserResult.Value;
            var newUsername = await GetUniqueUsername();
            await _userManager.SetUserNameAsync(identityUser, newUsername);

            identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<User>();
            }

            identityUser = identityUserResult.Value;
            var user = identityUser.Adapt<User>();

            return Result.Ok(user);
        }

        public async Task<Result<User>> ChangeUsername(int id, string username)
        {
            var identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<User>();
            }

            var identityUser = identityUserResult.Value;
            await _userManager.SetUserNameAsync(identityUser, username);

            identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<User>();
            }

            identityUser = identityUserResult.Value;
            var user = identityUser.Adapt<User>();

            return Result.Ok(user);
        }

        public async Task<Result<User>> SetEmailAsync(
            int id,
            string email,
            bool isConfirmed = false
        )
        {
            var identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<User>();
            }

            var identityUser = identityUserResult.Value;
            var setEmailResult = await _userManager.SetEmailAsync(identityUser, email);
            if (!setEmailResult.Succeeded)
            {
                return Result.Fail(
                    new ExceptionalError(new InvalidOperationException("Failed to set email"))
                );
            }

            identityUser.EmailConfirmed = isConfirmed;
            await _userManager.UpdateAsync(identityUser);

            identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<User>();
            }

            identityUser = identityUserResult.Value;
            var user = identityUser.Adapt<User>();

            return Result.Ok(user);
        }

        public async Task<Result<string>> GetJwtTokenAsync(User user)
        {
            var identityUserResult = await GetIdentityUserById(user.Id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<string>();
            }

            var identityUser = identityUserResult.Value;
            var signingCredentials = GetTokenSigningCredentials();
            var claims = await GetTokenClaimsAsync(identityUser);
            var tokenOptions = GetJwtTokenOptions(claims, signingCredentials);
            var token = new JwtSecurityTokenHandler().WriteToken(tokenOptions);

            return Result.Ok(token);
        }

        public async Task<Result<string>> GenerateConfirmationTokenAsync(User user)
        {
            var identityUserResult = await GetIdentityUserById(user.Id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<string>();
            }

            var identityUser = identityUserResult.Value;
            var confirmationToken = await _userManager.GenerateUserTokenAsync(
                identityUser,
                _confirmationTokenProviderOptions.Name,
                CONFIRMATION_TOKEN_PURPOSE
            );

            return Result.Ok(confirmationToken);
        }

        public async Task<Result<bool>> VerifyConfirmationTokenAsync(int id, string token)
        {
            var identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult();
            }

            var identityUser = identityUserResult.Value;
            var isTokenValid = await _userManager.VerifyUserTokenAsync(
                identityUser,
                _confirmationTokenProviderOptions.Name,
                CONFIRMATION_TOKEN_PURPOSE,
                token
            );

            if (!isTokenValid)
            {
                return Result.Fail(new InvalidError("Invalid token"));
            }

            identityUser.EmailConfirmed = true;
            await _userManager.UpdateAsync(identityUser);

            return Result.Ok(true);
        }

        public async Task<Result<string>> GenerateRecoveryTokenAsync(User user)
        {
            var identityUserResult = await GetIdentityUserById(user.Id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult<string>();
            }

            var identityUser = identityUserResult.Value;
            var recoveryToken = await _userManager.GenerateUserTokenAsync(
                identityUser,
                _recoveryTokenProviderOptions.Name,
                RECOVERY_TOKEN_PURPOSE
            );

            return Result.Ok(recoveryToken);
        }

        public async Task<Result<bool>> VerifyRecoveryTokenAsync(int id, string token)
        {
            var identityUserResult = await GetIdentityUserById(id);
            if (identityUserResult.IsFailed)
            {
                return identityUserResult.ToResult();
            }

            var identityUser = identityUserResult.Value;
            var isTokenValid = await _userManager.VerifyUserTokenAsync(
                identityUser,
                _recoveryTokenProviderOptions.Name,
                RECOVERY_TOKEN_PURPOSE,
                token
            );

            if (!isTokenValid)
            {
                return Result.Fail(new InvalidError("Invalid token"));
            }

            return Result.Ok(true);
        }

        public async Task<bool> IsUsernameAvailableAsync(string username)
        {
            var user = await _userManager.FindByNameAsync(username);

            return user is null;
        }

        public async Task<bool> IsEmailAvailableAsync(string email, int allowedUserId)
        {
            var user = await _userManager.FindByEmailAsync(email);

            return user is null || user.Id == allowedUserId;
        }

        private async Task<Result<ApplicationUser>> GetIdentityUserById(int id)
        {
            var identityUser = await _userManager.FindByIdAsync(id.ToString());
            if (identityUser is null)
            {
                return Result.Fail(new NotFoundError("User not found"));
            }

            return Result.Ok(identityUser);
        }

        private async Task<Result<ApplicationUser>> GetIdentityUserByEmail(string email)
        {
            var identityUser = await _userManager.FindByEmailAsync(email);
            if (identityUser is null)
            {
                return Result.Fail(new NotFoundError("User not found"));
            }

            return Result.Ok(identityUser);
        }

        private SigningCredentials GetTokenSigningCredentials()
        {
            var keyBytes = Encoding.UTF8.GetBytes(_jwtOptions.Secret);
            var secret = new SymmetricSecurityKey(keyBytes);
            var signingCredentials = new SigningCredentials(secret, SecurityAlgorithms.HmacSha256);

            return signingCredentials;
        }

        private async Task<IEnumerable<Claim>> GetTokenClaimsAsync(ApplicationUser user)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.UserName),
            };

            var roles = await _userManager.GetRolesAsync(user);
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            return claims;
        }

        private JwtSecurityToken GetJwtTokenOptions(
            IEnumerable<Claim> claims,
            SigningCredentials signingCredentials
        )
        {
            var tokenOptions = new JwtSecurityToken(
                issuer: _jwtOptions.ValidIssuer,
                audience: _jwtOptions.ValidAudience,
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(_jwtOptions.ExpiresInMinutes),
                signingCredentials: signingCredentials
            );

            return tokenOptions;
        }

        private async Task<string> GetUniqueUsername()
        {
            var username = string.Empty;
            while (username == string.Empty)
            {
                var tempUsername = _usernameService.GetRandomUsername();
                if (await IsUsernameAvailableAsync(tempUsername))
                {
                    username = tempUsername;
                }
            }

            return username;
        }
    }
}
