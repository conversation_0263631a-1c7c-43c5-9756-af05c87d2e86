﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WatchBackend.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddVideoWidthHeight : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Height",
                table: "VideoSourceEntity",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Width",
                table: "VideoSourceEntity",
                type: "INTEGER",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Height",
                table: "VideoSourceEntity");

            migrationBuilder.DropColumn(
                name: "Width",
                table: "VideoSourceEntity");
        }
    }
}
